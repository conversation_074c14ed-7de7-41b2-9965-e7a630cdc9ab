<?php

/**
 * OAuth Flow Test Script
 * 
 * This script tests the OAuth flow in different modes:
 * 1. Demo mode (mock data)
 * 2. Token mode (personal access token)
 * 3. Real mode (actual OAuth flow)
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\Auth\BexioAuthController;
use App\Services\BexioServiceInterface;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "🔐 Kim Rebill OAuth Flow Test\n";
echo "============================\n\n";

// Test 1: Demo Mode
echo "1️⃣  Testing Demo Mode OAuth Flow\n";
echo "--------------------------------\n";

try {
    // Set demo mode
    config(['bexio.mode' => 'demo']);
    
    // Create a mock request for OAuth callback
    $request = Request::create('/auth/callback', 'GET', ['code' => 'mock_code']);
    $app->instance('request', $request);
    
    // Test the controller
    $controller = new BexioAuthController(app(BexioServiceInterface::class));
    $response = $controller->callback();
    
    if ($response->isRedirection() && str_contains($response->headers->get('Location'), 'dashboard')) {
        echo "✅ Demo mode OAuth callback successful\n";
    } else {
        echo "❌ Demo mode OAuth callback failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Demo mode test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Personal Token Mode
echo "2️⃣  Testing Personal Token Mode\n";
echo "-------------------------------\n";

try {
    // Set token mode
    config(['bexio.mode' => 'token']);
    
    // Create a mock request for personal token login
    $request = Request::create('/auth/personal-token', 'GET');
    $app->instance('request', $request);
    
    // Test the controller
    $controller = new BexioAuthController(app(BexioServiceInterface::class));
    $response = $controller->personalTokenLogin();
    
    if ($response->isRedirection() && str_contains($response->headers->get('Location'), 'dashboard')) {
        echo "✅ Personal token login successful\n";
    } else {
        echo "❌ Personal token login failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Personal token test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Configuration Validation
echo "3️⃣  Testing Configuration\n";
echo "------------------------\n";

$requiredConfigs = [
    'bexio.client_id' => config('bexio.client_id'),
    'bexio.client_secret' => config('bexio.client_secret'),
    'bexio.redirect_uri' => config('bexio.redirect_uri'),
    'bexio.personal_token' => config('bexio.personal_token'),
];

foreach ($requiredConfigs as $key => $value) {
    if ($value) {
        echo "✅ {$key}: configured\n";
    } else {
        echo "❌ {$key}: missing\n";
    }
}

echo "\n";

// Test 4: Real OAuth URL Generation
echo "4️⃣  Testing Real OAuth URL Generation\n";
echo "------------------------------------\n";

try {
    config(['bexio.mode' => 'real']);
    
    $clientId = config('bexio.client_id');
    $redirectUri = config('bexio.redirect_uri');
    $scopes = implode(' ', config('bexio.scopes'));
    
    $authUrl = "https://auth.bexio.com/oauth/authorize?" . http_build_query([
        'client_id' => $clientId,
        'redirect_uri' => $redirectUri,
        'response_type' => 'code',
        'scope' => $scopes,
        'state' => 'test_state'
    ]);
    
    echo "✅ OAuth URL generated successfully\n";
    echo "🔗 URL: {$authUrl}\n";
    
} catch (Exception $e) {
    echo "❌ OAuth URL generation failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Database Connection
echo "5️⃣  Testing Database Connection\n";
echo "------------------------------\n";

try {
    $pdo = new PDO(
        'mysql:host=' . config('database.connections.mysql.host') . ';dbname=' . config('database.connections.mysql.database'),
        config('database.connections.mysql.username'),
        config('database.connections.mysql.password')
    );
    
    echo "✅ Database connection successful\n";
    
    // Check if required tables exist
    $tables = ['users', 'organizations'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '{$table}' exists\n";
        } else {
            echo "❌ Table '{$table}' missing\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Service Binding
echo "6️⃣  Testing Service Binding\n";
echo "---------------------------\n";

try {
    $bexioService = app(BexioServiceInterface::class);
    echo "✅ BexioService binding successful\n";
    echo "📋 Service class: " . get_class($bexioService) . "\n";
    
} catch (Exception $e) {
    echo "❌ Service binding failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Summary
echo "📊 Test Summary\n";
echo "==============\n";
echo "Run the following commands to execute the full test suite:\n\n";
echo "# Run OAuth feature tests\n";
echo "php artisan test tests/Feature/BexioOAuthTest.php\n\n";
echo "# Run middleware tests\n";
echo "php artisan test tests/Feature/BexioAuthMiddlewareTest.php\n\n";
echo "# Run helper unit tests\n";
echo "php artisan test tests/Unit/BexioAuthHelperTest.php\n\n";
echo "# Run all OAuth-related tests\n";
echo "php artisan test --filter=Bexio\n\n";

echo "🌐 Manual Testing URLs:\n";
echo "======================\n";
echo "1. Login page: http://127.0.0.1:8000/login\n";
echo "2. OAuth redirect: http://127.0.0.1:8000/auth/bexio\n";
echo "3. Personal token login: http://127.0.0.1:8000/auth/personal-token\n";
echo "4. Dashboard: http://127.0.0.1:8000/dashboard\n\n";

echo "✨ Test completed!\n";
