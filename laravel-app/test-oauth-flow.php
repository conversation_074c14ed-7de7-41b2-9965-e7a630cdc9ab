<?php

/**
 * OAuth Flow Test Script
 *
 * This script tests the real OAuth flow with Bexio
 */

echo "🔐 Kim <PERSON>bill OAuth Flow Test\n";
echo "============================\n\n";

// Test 1: Environment Configuration
echo "1️⃣  Testing Environment Configuration\n";
echo "------------------------------------\n";

// Load .env file manually
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env = [];
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            [$key, $value] = explode('=', $line, 2);
            $env[trim($key)] = trim($value, '"');
        }
    }

    $requiredConfigs = [
        'BEXIO_CLIENT_ID',
        'BEXIO_CLIENT_SECRET',
        'BEXIO_REDIRECT_URI'
    ];

    foreach ($requiredConfigs as $key) {
        if (isset($env[$key]) && !empty($env[$key])) {
            echo "✅ {$key}: configured\n";
        } else {
            echo "❌ {$key}: missing\n";
        }
    }

    echo "\n";

    // Test 2: OAuth URL Generation
    echo "2️⃣  Testing OAuth URL Generation\n";
    echo "--------------------------------\n";

    if (isset($env['BEXIO_CLIENT_ID']) && isset($env['BEXIO_REDIRECT_URI'])) {
        $scopes = [
            'openid',
            'profile',
            'email',
            'offline_access',
            'kb_invoice_edit',
            'contact_edit',
            'company_profile'
        ];

        $authUrl = "https://auth.bexio.com/oauth/authorize?" . http_build_query([
            'client_id' => $env['BEXIO_CLIENT_ID'],
            'redirect_uri' => $env['BEXIO_REDIRECT_URI'],
            'response_type' => 'code',
            'scope' => implode(' ', $scopes),
            'state' => 'test_state_' . time()
        ]);

        echo "✅ OAuth URL generated successfully\n";
        echo "🔗 URL: {$authUrl}\n";
    } else {
        echo "❌ Cannot generate OAuth URL - missing credentials\n";
    }

} else {
    echo "❌ .env file not found\n";
}

echo "\n";

// Test 3: Database Connection
echo "3️⃣  Testing Database Connection\n";
echo "------------------------------\n";

if (isset($env)) {
    try {
        $host = $env['DB_HOST'] ?? '127.0.0.1';
        $database = $env['DB_DATABASE'] ?? 'kim_rebill';
        $username = $env['DB_USERNAME'] ?? 'root';
        $password = $env['DB_PASSWORD'] ?? 'root';

        $pdo = new PDO(
            "mysql:host={$host};dbname={$database}",
            $username,
            $password
        );

        echo "✅ Database connection successful\n";

        // Check if required tables exist
        $tables = ['users', 'organizations'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "✅ Table '{$table}' exists\n";
            } else {
                echo "❌ Table '{$table}' missing\n";
            }
        }

    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Summary
echo "📊 Test Summary\n";
echo "==============\n";
echo "🌐 Manual Testing URLs:\n";
echo "======================\n";
echo "1. Login page: http://127.0.0.1:8000/login\n";
echo "2. OAuth redirect: http://127.0.0.1:8000/auth/bexio\n";
echo "3. Dashboard: http://127.0.0.1:8000/dashboard\n\n";

echo "🚀 To test OAuth flow:\n";
echo "======================\n";
echo "1. Start Laravel server: php artisan serve\n";
echo "2. Visit: http://127.0.0.1:8000/login\n";
echo "3. Click 'Login with Bexio'\n";
echo "4. Complete OAuth flow with your Bexio account\n\n";

echo "✨ Test completed!\n";
