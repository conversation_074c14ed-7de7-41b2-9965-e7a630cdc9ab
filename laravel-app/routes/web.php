<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\BexioAuthController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\SubscriptionController;
use App\Services\BexioServiceInterface;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Auth::check() ? redirect('/dashboard') : redirect('/login');
});

Route::get('/login', [BexioAuthController::class, 'showLoginForm'])
    ->name('login')
    ->middleware('guest');

Route::get('/auth/bexio', [BexioAuthController::class, 'redirect'])
    ->name('bexio.login')
    ->middleware('guest');

Route::get('/auth/callback', [BexioAuthController::class, 'callback'])
    ->name('bexio.auth.callback');

// Personal token login route
Route::get('/auth/personal-token', [BexioAuthController::class, 'personalTokenLogin'])
    ->name('bexio.personal-token-login');

Route::post('/logout', [BexioAuthController::class, 'logout'])
    ->name('logout')
    ->middleware('auth');

Route::get('/home', function () {
    return redirect('/dashboard');
});

Route::get('/dashboard', function () {
    $user = Auth::user();
    $bexioService = app(BexioServiceInterface::class);

    // Set access token from user if available
    if ($user && $user->access_token && method_exists($bexioService, 'setAccessToken')) {
        $bexioService->setAccessToken($user->access_token);
    }

    $invoices = $bexioService->getInvoices();
    $recurringInvoices = $bexioService->getRecurringInvoices();
    $drafts = $bexioService->getDrafts();

    return view('dashboard', [
        'invoices' => $invoices,
        'recurringInvoices' => $recurringInvoices,
        'drafts' => $drafts,
        'unpaidInvoices' => collect($invoices)->where('status', '!=', 'paid')->count(),
        'totalRevenue' => collect($invoices)->where('status', 'paid')->sum('total')
    ]);
})->middleware(['auth', 'fresh-token'])->name('dashboard');

// Subscription routes (accessible even with inactive subscription)
Route::middleware('auth')->group(function () {
    Route::post('/subscription/request-activation', [SubscriptionController::class, 'requestActivation'])
        ->name('subscription.request-activation');
});

// Invoice routes
Route::middleware(['auth', 'fresh-token'])->group(function () {
    Route::get('/invoices', [InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/create', [InvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [InvoiceController::class, 'store'])->name('invoices.store');
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/edit', [InvoiceController::class, 'edit'])->name('invoices.edit');
    Route::put('/invoices/{invoice}', [InvoiceController::class, 'update'])->name('invoices.update');
    Route::delete('/invoices/{invoice}', [InvoiceController::class, 'destroy'])->name('invoices.destroy');
});

// Settings routes
Route::middleware(['auth', 'fresh-token'])->group(function () {
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings/profile', [SettingsController::class, 'updateProfile'])->name('settings.profile.update');
    Route::put('/settings/password', [SettingsController::class, 'updatePassword'])->name('settings.password.update');
    Route::put('/settings/bexio', [SettingsController::class, 'updateBexioSettings'])->name('settings.bexio.update');
    Route::post('/settings/bexio/reset', [SettingsController::class, 'resetBexioConnection'])->name('settings.bexio.reset');
});
