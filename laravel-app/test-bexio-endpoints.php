<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "🔧 Testing Different Bexio API Endpoints\n";
echo "========================================\n\n";

// Get user with valid access token
$user = User::whereNotNull('access_token')->first();

if (!$user) {
    echo "❌ No user with access token found. Please login first.\n";
    exit(1);
}

echo "👤 User: {$user->name}\n";
echo "🔑 Scopes configured: " . implode(', ', config('bexio.scopes')) . "\n\n";

// Test different possible endpoints
$endpoints = [
    // Contacts
    '2.0/contact' => 'Contacts v2.0',
    '3.0/contact' => 'Contacts v3.0',
    '2.0/contacts' => 'Contacts v2.0 (plural)',
    '3.0/contacts' => 'Contacts v3.0 (plural)',

    // Units
    '2.0/unit' => 'Units',
    '2.0/units' => 'Units (plural)',

    // Taxes
    '2.0/tax' => 'Tax',
    '2.0/taxes' => 'Taxes (plural)',
    '3.0/tax' => 'Tax v3.0',
    '3.0/taxes' => 'Taxes v3.0 (plural)',

    // Other common endpoints
    '2.0/company_profile' => 'Company Profile',
    '2.0/user_management/users' => 'Users',
    '2.0/kb_invoice' => 'Invoices',
];

$headers = [
    'Accept' => 'application/json',
    'Authorization' => 'Bearer ' . $user->access_token,
    'Content-Type' => 'application/json'
];

$client = new \GuzzleHttp\Client();

foreach ($endpoints as $endpoint => $description) {
    echo "🔗 {$description} ({$endpoint}): ";

    $url = 'https://api.bexio.com/' . $endpoint;

    try {
        $response = $client->request('GET', $url, [
            'headers' => $headers,
            'timeout' => 10
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        if ($statusCode == 200) {
            if (is_array($data)) {
                echo "✅ {$statusCode} - " . count($data) . " records\n";
            } else {
                echo "✅ {$statusCode} - Response received\n";
            }
        } else {
            echo "⚠️  {$statusCode}\n";
        }

    } catch (\GuzzleHttp\Exception\ClientException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
        echo "❌ {$statusCode}";

        if ($statusCode == 403) {
            echo " (Forbidden - check scopes)";
        } elseif ($statusCode == 404) {
            echo " (Not Found)";
        } elseif ($statusCode == 401) {
            echo " (Unauthorized)";
        }
        echo "\n";

    } catch (\Exception $e) {
        echo "❌ Exception: " . substr($e->getMessage(), 0, 50) . "...\n";
    }
}

echo "\n🔍 Checking Current Scopes\n";
echo "=========================\n";

$configuredScopes = config('bexio.scopes');
echo "📋 Configured scopes:\n";
foreach ($configuredScopes as $scope) {
    echo "   - " . trim($scope) . "\n";
}

echo "\n💡 Recommendations:\n";
echo "==================\n";
echo "1. Check if contact endpoints need different scopes\n";
echo "2. Try 'contact_show' or 'contact_read' scope\n";
echo "3. Use working endpoints (units) for now\n";
echo "4. Check Bexio API documentation for correct endpoints\n";

echo "\n✨ Test completed!\n";
