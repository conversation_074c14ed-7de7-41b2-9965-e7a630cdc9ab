<?php

/**
 * Test OAuth Callback Flow
 * 
 * This script tests the OAuth callback to ensure no "Session expired" errors
 */

echo "🔐 Testing OAuth Callback Flow\n";
echo "==============================\n\n";

// Test 1: Check if callback route is accessible
echo "1️⃣  Testing OAuth Callback Route\n";
echo "--------------------------------\n";

$callbackUrl = "http://127.0.0.1:8000/auth/callback?code=test_code&state=test_state";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callbackUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: {$httpCode}\n";

if ($httpCode == 302) {
    echo "✅ Callback route accessible (redirecting as expected)\n";
    
    // Check if redirect is to dashboard or login
    if (strpos($response, 'Location:') !== false) {
        preg_match('/Location:\s*(.+)/', $response, $matches);
        if (isset($matches[1])) {
            $location = trim($matches[1]);
            echo "🔗 Redirect to: {$location}\n";
            
            if (strpos($location, 'dashboard') !== false) {
                echo "✅ Redirecting to dashboard (OAuth success)\n";
            } elseif (strpos($location, 'login') !== false) {
                echo "⚠️  Redirecting to login (check for errors)\n";
            }
        }
    }
} elseif ($httpCode == 500) {
    echo "❌ Internal server error - check logs\n";
} else {
    echo "⚠️  Unexpected status code: {$httpCode}\n";
}

echo "\n";

// Test 2: Check recent logs for errors
echo "2️⃣  Checking Recent Logs\n";
echo "-----------------------\n";

$logFile = __DIR__ . '/storage/logs/laravel-' . date('Y-m-d') . '.log';

if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    $recentLines = array_slice($lines, -20); // Last 20 lines
    
    $hasErrors = false;
    foreach ($recentLines as $line) {
        if (strpos($line, 'ERROR') !== false && strpos($line, date('Y-m-d')) !== false) {
            echo "❌ Recent error: " . substr($line, 0, 100) . "...\n";
            $hasErrors = true;
        }
    }
    
    if (!$hasErrors) {
        echo "✅ No recent errors in logs\n";
    }
} else {
    echo "⚠️  Log file not found: {$logFile}\n";
}

echo "\n";

// Test 3: Database check
echo "3️⃣  Database Structure Check\n";
echo "---------------------------\n";

try {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=kim_rebill',
        'root',
        'root'
    );
    
    // Check if required columns exist in users table
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = [
        'is_admin',
        'access_token', 
        'refresh_token',
        'token_expires_at',
        'organization_id'
    ];
    
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columns)) {
            echo "✅ Column '{$column}' exists\n";
        } else {
            echo "❌ Column '{$column}' missing\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database check failed: " . $e->getMessage() . "\n";
}

echo "\n";

echo "📊 Test Summary\n";
echo "==============\n";
echo "If all tests pass, OAuth callback should work without 'Session expired' errors.\n";
echo "If there are issues, check the specific error messages above.\n\n";

echo "🚀 To test manually:\n";
echo "1. Visit: http://127.0.0.1:8000/login\n";
echo "2. Click 'Login with Bexio'\n";
echo "3. Complete OAuth flow\n";
echo "4. Should redirect to dashboard without 'Session expired' message\n\n";

echo "✨ Test completed!\n";
