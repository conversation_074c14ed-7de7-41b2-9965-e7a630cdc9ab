<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 Testing Contact API with Personal Access Token\n";
echo "================================================\n\n";

// Check Personal Token configuration
$personalToken = config('bexio.personal_access_token');
$usePersonalToken = config('bexio.use_personal_token');

echo "🔑 Personal Token configured: " . ($personalToken ? 'YES' : 'NO') . "\n";
echo "🔄 Use Personal Token: " . ($usePersonalToken ? 'YES' : 'NO') . "\n";

if ($personalToken) {
    echo "📄 Token preview: " . substr($personalToken, 0, 50) . "...\n";
}

if (!$personalToken || !$usePersonalToken) {
    echo "❌ Personal token not configured properly\n";
    exit(1);
}

echo "\n🔗 Testing Contact Endpoints\n";
echo "============================\n";

$headers = [
    'Accept' => 'application/json',
    'Authorization' => 'Bearer ' . $personalToken,
    'Content-Type' => 'application/json'
];

$client = new \GuzzleHttp\Client();

// Test different contact endpoints
$endpoints = [
    '2.0/contact' => 'Contacts v2.0',
    '3.0/contact' => 'Contacts v3.0',
];

foreach ($endpoints as $endpoint => $description) {
    echo "📞 {$description}: ";
    
    try {
        $response = $client->request('GET', 'https://api.bexio.com/' . $endpoint, [
            'headers' => $headers,
            'timeout' => 10
        ]);
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);
        
        if ($statusCode == 200 && is_array($data)) {
            echo "✅ {$statusCode} - " . count($data) . " contacts\n";
            
            // Filter customers vs suppliers
            $customers = array_filter($data, function($contact) {
                return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 1;
            });
            
            $suppliers = array_filter($data, function($contact) {
                return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 2;
            });
            
            echo "   👥 Customers (type 1): " . count($customers) . "\n";
            echo "   🏢 Suppliers (type 2): " . count($suppliers) . "\n";
            
            if (count($customers) > 0) {
                echo "   📋 Sample customers:\n";
                $sampleCustomers = array_slice($customers, 0, 3);
                foreach ($sampleCustomers as $customer) {
                    $name = $customer['name_1'] ?? 'Unknown';
                    $email = $customer['mail'] ?? 'No email';
                    $id = $customer['id'] ?? 'No ID';
                    echo "      - ID: {$id}, Name: {$name}, Email: {$email}\n";
                }
            }
            
            // Show sample record structure
            if (count($data) > 0) {
                echo "   🔍 Sample record fields:\n";
                $sample = $data[0];
                $fieldCount = 0;
                foreach ($sample as $key => $value) {
                    if ($fieldCount >= 8) break; // Limit display
                    if (is_string($value) || is_numeric($value)) {
                        $displayValue = strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value;
                        echo "      {$key}: {$displayValue}\n";
                        $fieldCount++;
                    }
                }
            }
            
        } else {
            echo "⚠️  {$statusCode}\n";
        }
        
    } catch (\GuzzleHttp\Exception\ClientException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
        $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : '';
        
        echo "❌ {$statusCode}";
        
        if ($statusCode == 403) {
            echo " (Forbidden)\n";
        } elseif ($statusCode == 404) {
            echo " (Not Found)\n";
        } elseif ($statusCode == 401) {
            echo " (Unauthorized)\n";
        } else {
            echo "\n";
        }
        
        if ($responseBody) {
            $errorData = json_decode($responseBody, true);
            if ($errorData && isset($errorData['message'])) {
                echo "   📄 Error: {$errorData['message']}\n";
            } else {
                echo "   📄 Response: " . substr($responseBody, 0, 100) . "...\n";
            }
        }
        
    } catch (\Exception $e) {
        echo "❌ Exception: " . substr($e->getMessage(), 0, 50) . "...\n";
    }
    
    echo "\n";
}

echo "🔗 Testing Other Working Endpoints\n";
echo "==================================\n";

$otherEndpoints = [
    '2.0/unit' => 'Units',
    '3.0/taxes' => 'Taxes',
    '2.0/company_profile' => 'Company Profile'
];

foreach ($otherEndpoints as $endpoint => $description) {
    echo "📊 {$description}: ";
    
    try {
        $response = $client->request('GET', 'https://api.bexio.com/' . $endpoint, [
            'headers' => $headers,
            'timeout' => 5
        ]);
        
        $statusCode = $response->getStatusCode();
        if ($statusCode == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            echo "✅ {$statusCode} - " . (is_array($data) ? count($data) . " records" : "OK") . "\n";
        } else {
            echo "⚠️  {$statusCode}\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Error\n";
    }
}

echo "\n✨ Test completed!\n";
