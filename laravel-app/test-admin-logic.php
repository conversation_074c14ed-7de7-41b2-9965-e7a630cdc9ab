<?php

/**
 * Test Admin Logic Implementation
 * 
 * This script tests the admin assignment logic
 */

echo "🔐 Testing Admin Logic Implementation\n";
echo "====================================\n\n";

// Test 1: Check database structure
echo "1️⃣  Testing Database Structure\n";
echo "------------------------------\n";

try {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=kim_rebill',
        'root',
        'root'
    );
    
    // Check users table structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['is_admin', 'role', 'organization_id'];
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columns)) {
            echo "✅ Column '{$column}' exists\n";
        } else {
            echo "❌ Column '{$column}' missing\n";
        }
    }
    
    // Check organizations table structure
    $stmt = $pdo->query("DESCRIBE organizations");
    $orgColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredOrgColumns = ['address', 'zip', 'city', 'contact_name', 'phone', 'refresh_token'];
    foreach ($requiredOrgColumns as $column) {
        if (in_array($column, $orgColumns)) {
            echo "✅ Organization column '{$column}' exists\n";
        } else {
            echo "❌ Organization column '{$column}' missing\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database check failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check existing users and their roles
echo "2️⃣  Testing Existing Users\n";
echo "-------------------------\n";

try {
    $stmt = $pdo->query("
        SELECT u.id, u.name, u.email, u.is_admin, u.role, u.organization_id, o.name as org_name
        FROM users u 
        LEFT JOIN organizations o ON u.organization_id = o.id 
        ORDER BY u.organization_id, u.created_at
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "ℹ️  No users found in database\n";
    } else {
        echo "📋 Current users:\n";
        foreach ($users as $user) {
            $adminStatus = $user['is_admin'] ? '👑 Admin' : '👤 User';
            $role = $user['role'] ?? 'N/A';
            echo "  - {$user['name']} ({$user['email']}) | {$adminStatus} | Role: {$role} | Org: {$user['org_name']}\n";
        }
        
        // Check admin logic per organization
        $stmt = $pdo->query("
            SELECT organization_id, COUNT(*) as total_users, SUM(is_admin) as admin_count
            FROM users 
            GROUP BY organization_id
        ");
        $orgStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n📊 Organization Statistics:\n";
        foreach ($orgStats as $stat) {
            $orgId = $stat['organization_id'];
            $totalUsers = $stat['total_users'];
            $adminCount = $stat['admin_count'];
            
            echo "  - Org ID {$orgId}: {$totalUsers} users, {$adminCount} admin(s)";
            if ($adminCount == 1) {
                echo " ✅\n";
            } elseif ($adminCount == 0) {
                echo " ⚠️  (No admin!)\n";
            } else {
                echo " ⚠️  (Multiple admins!)\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ User check failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Simulate admin logic
echo "3️⃣  Testing Admin Assignment Logic\n";
echo "----------------------------------\n";

echo "📝 Admin Logic Rules:\n";
echo "  - First user in organization → is_admin=1, role='admin'\n";
echo "  - Subsequent users → is_admin=0, role='user'\n";
echo "  - Super admin → is_admin=1, role='super_admin' (manual)\n\n";

// Test the logic
try {
    // Test organization 1
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE organization_id = 1");
    $stmt->execute();
    $userCount = $stmt->fetchColumn();
    
    $isFirstUser = ($userCount === 0);
    $expectedAdmin = $isFirstUser ? 1 : 0;
    $expectedRole = $isFirstUser ? 'admin' : 'user';
    
    echo "🧪 Simulation for Organization ID 1:\n";
    echo "  - Current users: {$userCount}\n";
    echo "  - Is first user: " . ($isFirstUser ? 'Yes' : 'No') . "\n";
    echo "  - Expected is_admin: {$expectedAdmin}\n";
    echo "  - Expected role: {$expectedRole}\n";
    
} catch (Exception $e) {
    echo "❌ Logic test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check OAuth implementation
echo "4️⃣  Testing OAuth Implementation\n";
echo "-------------------------------\n";

$authControllerFile = __DIR__ . '/app/Http/Controllers/Auth/BexioAuthController.php';
if (file_exists($authControllerFile)) {
    $content = file_get_contents($authControllerFile);
    
    $checks = [
        'OAuth error handling' => 'request(\'error\')',
        'Company profile API' => 'company_profile',
        'First user check' => '$organization->users()->count() === 0',
        'Admin assignment' => '\'is_admin\' => $isFirstUser',
        'Role assignment' => '\'role\' => $isFirstUser ? \'admin\' : \'user\'',
    ];
    
    foreach ($checks as $feature => $pattern) {
        if (strpos($content, $pattern) !== false) {
            echo "✅ {$feature}: implemented\n";
        } else {
            echo "❌ {$feature}: missing\n";
        }
    }
} else {
    echo "❌ BexioAuthController not found\n";
}

echo "\n";

echo "📊 Test Summary\n";
echo "==============\n";
echo "✨ Admin logic implementation ready for testing!\n";
echo "\n🚀 To test manually:\n";
echo "1. Clear existing users: DELETE FROM users;\n";
echo "2. Visit: http://127.0.0.1:8000/login\n";
echo "3. Login with first Bexio account → should become admin\n";
echo "4. Login with second Bexio account from same org → should become user\n\n";

echo "✨ Test completed!\n";
