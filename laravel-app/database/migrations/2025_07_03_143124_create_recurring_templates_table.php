<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurring_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('contact_id')->nullable();
            $table->foreignId('invoice_id')->constrained();
            $table->string('title')->nullable();
            $table->json('positions')->nullable();
            $table->date('start_date')->nullable();
            $table->string('interval_str', 20)->nullable();
            $table->date('last_executed')->nullable();
            $table->enum('interval', ['daily','weekly','monthly']);
            $table->date('next_run');

            $table->timestamps();
            // Add index for contact_id
            $table->index(['organization_id', 'interval', 'contact_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurring_templates');
    }
};
