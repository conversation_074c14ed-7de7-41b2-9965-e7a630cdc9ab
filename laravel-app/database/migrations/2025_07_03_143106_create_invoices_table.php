<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained();
            $table->string('bexio_id');
            $table->string('document_nr');
            $table->json('contact_info');
            $table->decimal('total', 10, 2);
            $table->enum('status', ['draft','sent','paid','cancelled']);
            $table->boolean('is_recurring')->default(false);
            $table->json('recurring_settings')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
