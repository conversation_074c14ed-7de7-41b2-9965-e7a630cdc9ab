<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recurring_templates', function (Blueprint $table) {
            // Add missing columns to match PHP original
            $table->string('title')->nullable()->after('organization_id');
            $table->json('positions')->nullable()->after('title');
            $table->date('start_date')->nullable()->after('positions');
            $table->string('interval_str', 20)->nullable()->after('start_date');
            $table->date('last_executed')->nullable()->after('interval_str');
            $table->unsignedBigInteger('contact_id')->nullable()->after('organization_id');

            // Add index for contact_id
            $table->index('contact_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recurring_templates', function (Blueprint $table) {
            $table->dropColumn([
                'title',
                'positions',
                'start_date',
                'interval_str',
                'last_executed',
                'contact_id'
            ]);
        });
    }
};
