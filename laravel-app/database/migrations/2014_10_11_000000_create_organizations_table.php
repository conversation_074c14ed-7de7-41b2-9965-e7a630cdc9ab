<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('bexio_org_id')->unique();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('address')->nullable();
            $table->string('zip')->nullable();
            $table->string('city')->nullable();
            $table->string('contact_name')->nullable();
            $table->string('phone')->nullable();
            $table->text('refresh_token')->nullable();
            $table->string('country')->default('CH');
            $table->string('language')->default('de');
            $table->enum('subscription_status', ['trial', 'active', 'inactive'])->default('trial');
            $table->enum('subscription_model', ['monthly', 'yearly'])->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('subscription_start')->nullable();
            $table->timestamp('activated_at')->nullable();
            $table->json('bexio_company_profile')->nullable();

            $table->timestamps();

            $table->index(['subscription_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};
