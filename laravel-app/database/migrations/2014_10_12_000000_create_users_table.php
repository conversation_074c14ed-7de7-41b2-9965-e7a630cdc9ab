<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('bexio_id')->unique();
            $table->string('name');
            $table->string('email')->unique();

            // Bexio OAuth tokens - using names that match the code
            $table->text('access_token')->nullable();
            $table->text('refresh_token')->nullable();
            $table->timestamp('token_expires_at')->nullable();
            $table->timestamp('refresh_token_rotated_at')->nullable();

            // Organization relationship
            $table->unsignedBigInteger('organization_id')->nullable();
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');

            // User roles and status
            $table->boolean('is_admin')->default(false);
            $table->enum('role', ['user', 'admin', 'super_admin'])->default('user');
            $table->boolean('is_active')->default(true)->index();
            $table->timestamp('last_login_at')->nullable();

            // Bexio user profile data
            $table->longText('bexio_user_profile')->nullable();
            $table->string('bexio_company_id')->nullable();
            $table->string('bexio_user_id')->nullable();

            // Standard Laravel fields
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->rememberToken();
            $table->timestamps();

            // Indexes
            $table->index(['organization_id', 'is_active']);
            $table->index('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
