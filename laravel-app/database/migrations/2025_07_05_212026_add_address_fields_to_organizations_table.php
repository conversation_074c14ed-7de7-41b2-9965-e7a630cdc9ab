<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // Add address fields matching PHP original
            $table->string('address')->nullable()->after('email');
            $table->string('zip')->nullable()->after('address');
            $table->string('city')->nullable()->after('zip');
            $table->string('contact_name')->nullable()->after('city');
            $table->string('phone')->nullable()->after('contact_name');
            $table->text('refresh_token')->nullable()->after('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn([
                'address',
                'zip',
                'city',
                'contact_name',
                'phone',
                'refresh_token'
            ]);
        });
    }
};
