<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('bexio_organization_id')->unique();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('country')->default('CH');
            $table->string('language')->default('de');
            $table->enum('status', ['trial', 'active', 'inactive'])->default('trial');
            $table->enum('subscription_status', ['trial', 'active', 'inactive'])->default('trial');
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('activated_at')->nullable();
            $table->json('bexio_company_profile')->nullable();
            $table->timestamps();

            $table->index(['status']);
            $table->index(['subscription_status']);
            $table->index(['trial_ends_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};
