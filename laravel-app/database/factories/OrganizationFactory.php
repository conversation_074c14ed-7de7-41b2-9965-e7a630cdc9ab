<?php

namespace Database\Factories;

use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrganizationFactory extends Factory
{
    protected $model = Organization::class;

    public function definition()
    {
        return [
            'bexio_company_id' => $this->faker->uuid(),
            'name' => $this->faker->company(),
            'email' => $this->faker->companyEmail(),
            'country' => $this->faker->randomElement(['CH', 'DE', 'AT', 'FR']),
            'language' => $this->faker->randomElement(['de', 'en', 'fr', 'it']),
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(90), // 3 month trial
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the organization has an active subscription.
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'subscription_status' => 'active',
                'trial_ends_at' => now()->subDays(30), // Trial ended, but has paid subscription
            ];
        });
    }

    /**
     * Indicate that the organization has an inactive subscription.
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'subscription_status' => 'inactive',
                'trial_ends_at' => now()->subDays(30),
            ];
        });
    }

    /**
     * Indicate that the organization has an expired trial.
     */
    public function expiredTrial()
    {
        return $this->state(function (array $attributes) {
            return [
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->subDays(1), // Trial expired yesterday
            ];
        });
    }

    /**
     * Indicate that the organization has an active trial.
     */
    public function activeTrial()
    {
        return $this->state(function (array $attributes) {
            return [
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->addDays(30), // Trial active for 30 more days
            ];
        });
    }
}
