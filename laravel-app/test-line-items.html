<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Line Items</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Line Items Functionality</h2>
        
        <!-- Items Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Line Items</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 30%">Name</th>
                                <th style="width: 12%">Quantity</th>
                                <th style="width: 15%">Unit</th>
                                <th style="width: 15%">Unit Price</th>
                                <th style="width: 15%">Tax</th>
                                <th style="width: 10%">Total</th>
                                <th style="width: 3%"></th>
                            </tr>
                        </thead>
                        <tbody id="items-container">
                            <!-- Items will be added here dynamically -->
                        </tbody>
                    </table>
                </div>
                
                <button type="button" class="btn btn-outline-primary" id="add-item-btn">
                    <i class="fas fa-plus me-1"></i> Add Line Item
                </button>
                
                <hr>
                <div class="text-end">
                    <h5>Total: <span id="total-amount">0.00</span> CHF</h5>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Template (hidden) -->
    <template id="item-template">
        <tr class="item-row">
            <td>
                <textarea class="form-control item-name" name="items[INDEX][name]" rows="2" 
                          placeholder="Item name" required></textarea>
            </td>
            <td>
                <input type="number" class="form-control item-quantity" name="items[INDEX][quantity]" 
                       min="0" step="0.01" value="1" required>
            </td>
            <td>
                <select class="form-select item-unit" name="items[INDEX][unit_id]" required>
                    <option value="">Select unit...</option>
                    <option value="1">Pieces</option>
                    <option value="2">Hours</option>
                    <option value="3">Days</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control item-price" name="items[INDEX][unit_price]" 
                       min="0" step="0.01" value="0" required>
            </td>
            <td>
                <select class="form-select item-tax" name="items[INDEX][tax_id]" required>
                    <option value="">Select tax...</option>
                    <option value="1">7.7% VAT</option>
                    <option value="2">2.5% VAT</option>
                    <option value="3">0% VAT</option>
                </select>
            </td>
            <td>
                <div class="bg-light border rounded p-2 text-center">
                    <span class="item-total">0.00</span>
                </div>
            </td>
            <td>
                <button type="button" class="btn btn-outline-danger btn-sm remove-item-btn">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    </template>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        let itemIndex = 0;

        // Add item functionality
        document.getElementById('add-item-btn').addEventListener('click', function() {
            console.log('Add item button clicked');
            addItem();
        });

        function addItem() {
            console.log('Adding item with index:', itemIndex);
            
            const template = document.getElementById('item-template');
            const container = document.getElementById('items-container');
            
            if (!template) {
                console.error('Template not found');
                return;
            }
            
            if (!container) {
                console.error('Container not found');
                return;
            }
            
            const clone = template.content.cloneNode(true);
            
            // Replace INDEX with actual index
            const htmlContent = clone.querySelector('tr').outerHTML;
            const updatedHTML = htmlContent.replace(/INDEX/g, itemIndex);
            
            // Create new row element
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = updatedHTML;
            const newRow = tempDiv.firstChild;
            
            // Add event listeners for calculation
            addItemEventListeners(newRow);
            
            container.appendChild(newRow);
            itemIndex++;
            
            console.log('Item added successfully. Total items:', itemIndex);
            calculateTotal();
        }

        function addItemEventListeners(itemRow) {
            const quantityInput = itemRow.querySelector('.item-quantity');
            const priceInput = itemRow.querySelector('.item-price');
            const removeBtn = itemRow.querySelector('.remove-item-btn');

            if (quantityInput) {
                quantityInput.addEventListener('input', () => calculateItemTotal(itemRow));
            }
            
            if (priceInput) {
                priceInput.addEventListener('input', () => calculateItemTotal(itemRow));
            }
            
            if (removeBtn) {
                removeBtn.addEventListener('click', () => {
                    itemRow.remove();
                    calculateTotal();
                });
            }
        }

        function calculateItemTotal(itemRow) {
            const quantity = parseFloat(itemRow.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(itemRow.querySelector('.item-price').value) || 0;
            const total = quantity * price;
            
            itemRow.querySelector('.item-total').textContent = total.toFixed(2);
            calculateTotal();
        }

        function calculateTotal() {
            let total = 0;
            document.querySelectorAll('.item-total').forEach(element => {
                total += parseFloat(element.textContent) || 0;
            });

            document.getElementById('total-amount').textContent = total.toFixed(2);
        }

        // Add first item by default
        console.log('Adding first item by default');
        addItem();
    });
    </script>
</body>
</html>
