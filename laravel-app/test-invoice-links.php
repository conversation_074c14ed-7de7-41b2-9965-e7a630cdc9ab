<?php

/**
 * Test Invoice Links and Routes
 */

echo "🔗 Testing Invoice Links and Routes\n";
echo "==================================\n\n";

// Test routes
$routes = [
    'Basic Invoice Create' => '/invoices/create',
    'Advanced Invoice Create' => '/create_invoice',
    'Invoice Index' => '/invoices',
];

echo "📋 Available Routes:\n";
echo "===================\n";

foreach ($routes as $name => $url) {
    echo "✅ {$name}: {$url}\n";
}

echo "\n🎯 Route Mapping:\n";
echo "================\n";
echo "• route('invoices.create') → /invoices/create (Basic form)\n";
echo "• route('invoices.create-advanced') → /create_invoice (Advanced form)\n";
echo "• route('invoices.index') → /invoices (List view)\n";

echo "\n📱 UI Components Using Advanced Form:\n";
echo "====================================\n";

// Check dashboard links
$dashboardFile = __DIR__ . '/resources/views/dashboard.blade.php';
if (file_exists($dashboardFile)) {
    $content = file_get_contents($dashboardFile);
    
    $advancedLinks = substr_count($content, 'invoices.create-advanced');
    $basicLinks = substr_count($content, 'invoices.create');
    
    echo "📊 Dashboard Links:\n";
    echo "  - Advanced form links: {$advancedLinks}\n";
    echo "  - Basic form links: " . ($basicLinks - $advancedLinks) . "\n";
    
    if ($advancedLinks > 0) {
        echo "  ✅ Dashboard uses advanced invoice form\n";
    } else {
        echo "  ❌ Dashboard still uses basic form\n";
    }
} else {
    echo "❌ Dashboard file not found\n";
}

// Check dashboard-simple links
$dashboardSimpleFile = __DIR__ . '/resources/views/dashboard-simple.blade.php';
if (file_exists($dashboardSimpleFile)) {
    $content = file_get_contents($dashboardSimpleFile);
    
    if (strpos($content, '/create_invoice') !== false) {
        echo "  ✅ Dashboard-simple uses advanced invoice form\n";
    } else {
        echo "  ❌ Dashboard-simple uses different link\n";
    }
} else {
    echo "  ℹ️  Dashboard-simple file not found (optional)\n";
}

echo "\n🎨 Advanced Invoice Form Features:\n";
echo "=================================\n";

$advancedFormFile = __DIR__ . '/resources/views/invoices/create-advanced.blade.php';
if (file_exists($advancedFormFile)) {
    $content = file_get_contents($advancedFormFile);
    
    $features = [
        'Invoice Details Section' => 'Rechnungsdetails',
        'Customer Selection' => 'Kunde auswählen',
        'New Customer Form' => 'Neuer Kunde',
        'Billing Configuration' => 'Abrechnung',
        'Dynamic Items' => 'items-container',
        'Tax Configuration' => 'Steuerstatus',
        'JavaScript Functionality' => 'addEventListener',
        'AJAX Integration' => '/api/bexio/',
    ];
    
    foreach ($features as $feature => $searchTerm) {
        if (strpos($content, $searchTerm) !== false) {
            echo "  ✅ {$feature}\n";
        } else {
            echo "  ❌ {$feature}\n";
        }
    }
} else {
    echo "❌ Advanced form file not found\n";
}

echo "\n🔧 Required API Endpoints:\n";
echo "=========================\n";

$apiEndpoints = [
    '/api/bexio/customers' => 'Load customer list',
    '/api/bexio/units' => 'Load units for items',
    '/api/bexio/taxes' => 'Load tax options',
    '/api/bexio/create-customer' => 'Create new customer',
];

foreach ($apiEndpoints as $endpoint => $description) {
    echo "⚠️  {$endpoint} - {$description} (needs implementation)\n";
}

echo "\n📝 Form Comparison:\n";
echo "==================\n";

$basicFormFile = __DIR__ . '/resources/views/invoices/create.blade.php';
if (file_exists($basicFormFile)) {
    echo "📄 Basic Form (create.blade.php):\n";
    echo "  - Simple form fields\n";
    echo "  - Basic validation\n";
    echo "  - Standard Laravel form\n";
    echo "  - Route: /invoices/create\n\n";
} else {
    echo "❌ Basic form file not found\n\n";
}

if (file_exists($advancedFormFile)) {
    echo "🚀 Advanced Form (create-advanced.blade.php):\n";
    echo "  - Multi-section layout\n";
    echo "  - Customer creation\n";
    echo "  - Dynamic items management\n";
    echo "  - Real-time calculations\n";
    echo "  - AJAX integration\n";
    echo "  - German localization\n";
    echo "  - Route: /create_invoice\n";
}

echo "\n🎯 Recommendations:\n";
echo "==================\n";
echo "1. ✅ Use /create_invoice for main invoice creation\n";
echo "2. ⚠️  Implement required API endpoints\n";
echo "3. ⚠️  Add form validation and processing\n";
echo "4. ⚠️  Connect to Bexio API for real data\n";
echo "5. ✅ Keep basic form as fallback option\n";

echo "\n✨ Test completed!\n";
