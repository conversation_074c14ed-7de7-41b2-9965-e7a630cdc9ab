<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "🔧 Testing Different Contact Scopes\n";
echo "===================================\n\n";

// Get user with valid access token
$user = User::whereNotNull('access_token')->first();

if (!$user) {
    echo "❌ No user with access token found.\n";
    exit(1);
}

echo "👤 User: {$user->name}\n";
echo "🕐 Token expires: {$user->token_expires_at}\n\n";

// Test different possible scopes by trying different endpoints
$headers = [
    'Accept' => 'application/json',
    'Authorization' => 'Bearer ' . $user->access_token,
    'Content-Type' => 'application/json'
];

$client = new \GuzzleHttp\Client();

// Test endpoints that work to verify token is valid
echo "🔗 Testing Working Endpoints (to verify token)\n";
echo "==============================================\n";

$workingEndpoints = [
    '2.0/unit' => 'Units',
    '3.0/taxes' => 'Taxes',
    '2.0/company_profile' => 'Company Profile'
];

foreach ($workingEndpoints as $endpoint => $description) {
    echo "✅ {$description}: ";
    
    try {
        $response = $client->request('GET', 'https://api.bexio.com/' . $endpoint, [
            'headers' => $headers,
            'timeout' => 5
        ]);
        
        $statusCode = $response->getStatusCode();
        if ($statusCode == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            echo "✅ {$statusCode} - " . (is_array($data) ? count($data) . " records" : "OK") . "\n";
        } else {
            echo "⚠️  {$statusCode}\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Error\n";
    }
}

echo "\n🔗 Testing Contact-Related Endpoints\n";
echo "====================================\n";

// Try different contact-related endpoints
$contactEndpoints = [
    '2.0/contact' => 'Contacts v2.0',
    '3.0/contact' => 'Contacts v3.0',
    '2.0/contact_group' => 'Contact Groups',
    '2.0/contact_relation' => 'Contact Relations',
    '2.0/contact_sector' => 'Contact Sectors',
    '2.0/customer' => 'Customers',
    '2.0/supplier' => 'Suppliers',
];

foreach ($contactEndpoints as $endpoint => $description) {
    echo "📞 {$description}: ";
    
    try {
        $response = $client->request('GET', 'https://api.bexio.com/' . $endpoint, [
            'headers' => $headers,
            'timeout' => 5
        ]);
        
        $statusCode = $response->getStatusCode();
        if ($statusCode == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            echo "✅ {$statusCode} - " . (is_array($data) ? count($data) . " records" : "OK") . "\n";
        } else {
            echo "⚠️  {$statusCode}\n";
        }
        
    } catch (\GuzzleHttp\Exception\ClientException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
        echo "❌ {$statusCode}";
        
        if ($statusCode == 403) {
            echo " (Forbidden)";
        } elseif ($statusCode == 404) {
            echo " (Not Found)";
        }
        echo "\n";
        
    } catch (\Exception $e) {
        echo "❌ Exception\n";
    }
}

echo "\n🔍 Possible Solutions:\n";
echo "======================\n";
echo "1. Contact access might require different Bexio plan/subscription\n";
echo "2. Contact permissions might need to be enabled in Bexio dashboard\n";
echo "3. Try different scope combinations\n";
echo "4. Use mock data for development and real data for production\n";

echo "\n💡 Alternative Approach:\n";
echo "========================\n";
echo "Since Units and Taxes work, we can:\n";
echo "1. Use mock customer data for development\n";
echo "2. Focus on invoice creation functionality\n";
echo "3. Add real customer integration later when permissions are resolved\n";

echo "\n✨ Test completed!\n";
