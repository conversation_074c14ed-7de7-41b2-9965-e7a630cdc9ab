<?php

/**
 * Test Bexio Configuration from Environment Variables
 */

echo "🔧 Testing Bexio Configuration from .env\n";
echo "========================================\n\n";

// Load .env file manually
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env = [];
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            [$key, $value] = explode('=', $line, 2);
            $env[trim($key)] = trim($value, '"');
        }
    }
    
    echo "📋 Environment Variables:\n";
    echo "========================\n";
    
    $bexioVars = [
        'BEXIO_CLIENT_ID' => 'OAuth Client ID',
        'BEXIO_CLIENT_SECRET' => 'OAuth Client Secret',
        'BEXIO_REDIRECT_URI' => 'OAuth Redirect URI',
        'BEXIO_AUTH_BASE_URL' => 'Auth Base URL',
        'BEXIO_API_BASE_URL' => 'API Base URL',
        'BEXIO_AUTH_ENDPOINT' => 'Auth Endpoint',
        'BEXIO_TOKEN_ENDPOINT' => 'Token Endpoint',
        'BEXIO_USERINFO_ENDPOINT' => 'User Info Endpoint',
        'BEXIO_SCOPES' => 'OAuth Scopes',
        'BEXIO_AUTH_URL' => 'Legacy Auth URL',
        'BEXIO_TOKEN_URL' => 'Legacy Token URL',
        'BEXIO_TOKEN_EXPIRES_IN' => 'Token Expires In (seconds)',
        'BEXIO_TOKEN_REFRESH_THRESHOLD' => 'Token Refresh Threshold (seconds)',
    ];
    
    foreach ($bexioVars as $key => $description) {
        $value = $env[$key] ?? 'NOT SET';
        $status = isset($env[$key]) ? '✅' : '❌';
        
        // Mask sensitive values
        if (in_array($key, ['BEXIO_CLIENT_SECRET']) && $value !== 'NOT SET') {
            $value = substr($value, 0, 10) . '...';
        }
        
        echo "{$status} {$description}: {$value}\n";
    }
    
    echo "\n📊 Configuration Analysis:\n";
    echo "==========================\n";
    
    // Test scopes parsing
    if (isset($env['BEXIO_SCOPES'])) {
        $scopes = explode(',', $env['BEXIO_SCOPES']);
        echo "✅ Scopes parsed: " . count($scopes) . " scopes\n";
        foreach ($scopes as $scope) {
            echo "   - " . trim($scope) . "\n";
        }
    } else {
        echo "❌ Scopes not configured\n";
    }
    
    // Test token configuration
    $tokenExpiresIn = $env['BEXIO_TOKEN_EXPIRES_IN'] ?? 3600;
    $tokenRefreshThreshold = $env['BEXIO_TOKEN_REFRESH_THRESHOLD'] ?? 600;
    
    echo "\n🕐 Token Configuration:\n";
    echo "   - Token expires in: {$tokenExpiresIn} seconds (" . ($tokenExpiresIn / 60) . " minutes)\n";
    echo "   - Refresh threshold: {$tokenRefreshThreshold} seconds (" . ($tokenRefreshThreshold / 60) . " minutes before expiry)\n";
    
    if ($tokenRefreshThreshold >= $tokenExpiresIn) {
        echo "   ⚠️  WARNING: Refresh threshold should be less than token expiry time\n";
    } else {
        echo "   ✅ Token timing configuration looks good\n";
    }
    
    // Test session configuration
    $sessionLifetime = $env['SESSION_LIFETIME'] ?? 120;
    echo "\n🔐 Session Configuration:\n";
    echo "   - Session lifetime: {$sessionLifetime} minutes\n";
    
    if ($sessionLifetime < 60) {
        echo "   ⚠️  WARNING: Session lifetime is quite short (< 1 hour)\n";
    } else {
        echo "   ✅ Session lifetime looks reasonable\n";
    }
    
    echo "\n🔗 OAuth URL Generation Test:\n";
    echo "=============================\n";
    
    if (isset($env['BEXIO_AUTH_ENDPOINT']) && isset($env['BEXIO_CLIENT_ID']) && isset($env['BEXIO_REDIRECT_URI'])) {
        $authUrl = $env['BEXIO_AUTH_ENDPOINT'] . '?' . http_build_query([
            'client_id' => $env['BEXIO_CLIENT_ID'],
            'redirect_uri' => $env['BEXIO_REDIRECT_URI'],
            'response_type' => 'code',
            'scope' => $env['BEXIO_SCOPES'] ?? 'openid profile email',
            'state' => 'test_state_' . time()
        ]);
        
        echo "✅ OAuth URL generated successfully\n";
        echo "🔗 URL: " . substr($authUrl, 0, 100) . "...\n";
        
        // Validate URL components
        $urlParts = parse_url($authUrl);
        if ($urlParts && isset($urlParts['scheme']) && isset($urlParts['host'])) {
            echo "✅ URL structure is valid\n";
        } else {
            echo "❌ URL structure is invalid\n";
        }
    } else {
        echo "❌ Cannot generate OAuth URL - missing required configuration\n";
    }
    
} else {
    echo "❌ .env file not found\n";
}

echo "\n📝 Configuration Management Benefits:\n";
echo "====================================\n";
echo "✅ All Bexio settings now in .env file\n";
echo "✅ Easy to change environments (dev/staging/prod)\n";
echo "✅ Sensitive data not in version control\n";
echo "✅ Consistent configuration across team\n";
echo "✅ No need to edit PHP config files\n";

echo "\n🚀 Next Steps:\n";
echo "=============\n";
echo "1. Copy .env.example to .env for new environments\n";
echo "2. Update BEXIO_CLIENT_ID and BEXIO_CLIENT_SECRET\n";
echo "3. Adjust BEXIO_REDIRECT_URI for your domain\n";
echo "4. Run 'php artisan config:clear' after changes\n";

echo "\n✨ Test completed!\n";
