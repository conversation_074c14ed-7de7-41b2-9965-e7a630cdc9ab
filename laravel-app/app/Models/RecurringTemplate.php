<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RecurringTemplate extends Model
{
    protected $fillable = [
        'user_id',
        'organization_id',
        'invoice_id',
        'interval',
        'next_run'
    ];

    protected $casts = [
        'next_run' => 'date',
        'interval' => 'string'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    // Scopes
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeDue($query)
    {
        return $query->where('next_run', '<=', now());
    }

    public function scopeByInterval($query, $interval)
    {
        return $query->where('interval', $interval);
    }

    // Helper Methods
    public function isDue()
    {
        return $this->next_run <= now();
    }

    public function updateNextRun()
    {
        $nextRun = match($this->interval) {
            'daily' => $this->next_run->addDay(),
            'weekly' => $this->next_run->addWeek(),
            'monthly' => $this->next_run->addMonth(),
            default => $this->next_run->addMonth(),
        };

        $this->update(['next_run' => $nextRun]);
    }
}
