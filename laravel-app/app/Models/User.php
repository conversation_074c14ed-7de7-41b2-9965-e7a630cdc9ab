<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'bexio_id',
        'name',
        'email',
        'organization_id',
        'is_admin',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'refresh_token_rotated_at',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'token_expires_at' => 'datetime',
        'refresh_token_rotated_at' => 'datetime',
        'is_admin' => 'boolean',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function recurringTemplates()
    {
        return $this->hasMany(RecurringTemplate::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    // Helper Methods
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isSuperAdmin()
    {
        return $this->role === 'super_admin';
    }

    public function canManageOrganization()
    {
        return $this->isAdmin() || $this->isSuperAdmin();
    }

    public function hasActiveSubscription()
    {
        return $this->organization &&
               $this->organization->subscription &&
               $this->organization->subscription->isActive();
    }

    public function canCreateInvoices()
    {
        return $this->is_active &&
               $this->organization &&
               $this->organization->canCreateInvoices();
    }

    public function updateLastLogin()
    {
        $this->update(['last_login_at' => now()]);
    }
}
