<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'plan_type',
        'price',
        'status',
        'trial_ends_at',
        'current_period_start',
        'current_period_end',
        'cancelled_at',
        'payment_method',
        'billing_details',
        'notes',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'cancelled_at' => 'datetime',
        'billing_details' => 'array',
        'price' => 'decimal:2',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeTrial($query)
    {
        return $query->where('status', 'trial');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    // Helper Methods
    public function isActive()
    {
        return $this->status === 'active';
    }

    public function isTrial()
    {
        return $this->status === 'trial';
    }

    public function isExpired()
    {
        return $this->status === 'expired' ||
               ($this->current_period_end && $this->current_period_end->isPast());
    }

    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    public function getDaysUntilExpiry()
    {
        if (!$this->current_period_end) {
            return null;
        }

        return max(0, now()->diffInDays($this->current_period_end, false));
    }

    public function getMonthlyPrice()
    {
        return $this->plan_type === 'monthly' ? $this->price : $this->price / 12;
    }

    public function getYearlyPrice()
    {
        return $this->plan_type === 'yearly' ? $this->price : $this->price * 12;
    }

    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }

    public function activate($periodStart = null, $periodEnd = null)
    {
        $start = $periodStart ?: now();
        $end = $periodEnd ?: ($this->plan_type === 'yearly' ? $start->copy()->addYear() : $start->copy()->addMonth());

        $this->update([
            'status' => 'active',
            'current_period_start' => $start,
            'current_period_end' => $end,
            'cancelled_at' => null,
        ]);
    }
}
