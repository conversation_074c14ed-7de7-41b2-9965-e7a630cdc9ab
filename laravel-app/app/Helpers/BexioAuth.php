<?php

namespace App\Helpers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>jett\OpenIDConnectClient;

class BexioAuth
{
    /**
     * Require login and fresh token - matching PHP original requireLoginAndFreshToken
     */
    public static function requireLoginAndFreshToken()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Skip token refresh for very new users (created within last 5 minutes)
        // This prevents refresh attempts during OAuth callback flow
        if ($user->created_at && $user->created_at->diffInMinutes(now()) < 5) {
            Log::info("Skipping token refresh for new user {$user->id} (created {$user->created_at->diffInMinutes(now())} minutes ago)");
            return null;
        }

        // Check if token needs refresh (configurable threshold before expiry)
        if ($user->token_expires_at &&
            $user->token_expires_at->diffInSeconds(now()) < config('bexio.token_refresh_threshold')) {

            return self::refreshToken($user);
        }

        return null; // No redirect needed
    }

    /**
     * Refresh token using OpenIDConnectClient - matching PHP original
     */
    private static function refreshToken(User $user)
    {
        // Don't try to refresh if user doesn't have a refresh token (new user)
        if (!$user->refresh_token) {
            Log::info("Skipping token refresh for user {$user->id}: No refresh token available (likely new user)");
            return null; // No refresh needed for new users
        }

        try {
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            if (!$clientId || !$clientSecret) {
                Log::error('Bexio client credentials not configured');
                throw new \Exception('Bexio client credentials missing');
            }

            $oidc = new OpenIDConnectClient(
                config('bexio.oidc_issuer'),
                $clientId,
                $clientSecret
            );

            $oidc->providerConfigParam([
                'token_endpoint' => config('bexio.oidc_token_endpoint'),
            ]);

            // Refresh the token
            $oidc->refreshToken($user->refresh_token);

            $newAccessToken = $oidc->getAccessToken();
            $newRefreshToken = $oidc->getRefreshToken();

            if ($newAccessToken) {
                $user->update([
                    'access_token' => $newAccessToken,
                    'refresh_token' => $newRefreshToken ?: $user->refresh_token,
                    'token_expires_at' => now()->addSeconds(config('bexio.token_expires_in')),
                    'refresh_token_rotated_at' => now(),
                ]);

                Log::info("Token refreshed successfully for user {$user->id}");
                return null; // Success, no redirect needed
            } else {
                Log::error("Token refresh failed for user {$user->id}: No access token received");
            }

        } catch (\Exception $e) {
            Log::error("Token refresh failed for user {$user->id}: " . $e->getMessage());
        }

        // If refresh failed, logout and redirect to login
        // But don't show "session expired" message if this is during OAuth flow or for new users
        Auth::logout();
        $message = request()->is('auth/*') ? 'Authentication failed' : 'Session expired, please login again';
        return redirect()->route('login')->withErrors(['auth' => $message]);
    }

    /**
     * Get current user's access token
     */
    public static function getAccessToken()
    {
        $user = Auth::user();
        return $user ? $user->access_token : null;
    }

    /**
     * Check if user has admin privileges
     */
    public static function isAdmin()
    {
        $user = Auth::user();
        return $user && $user->is_admin;
    }
}
