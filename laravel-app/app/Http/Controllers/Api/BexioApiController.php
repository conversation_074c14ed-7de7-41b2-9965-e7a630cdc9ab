<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BexioApiController extends Controller
{
    private function makeApiRequest($endpoint)
    {
        $user = Auth::user();

        if (!$user || !$user->access_token) {
            return response()->json(['error' => 'No access token available'], 401);
        }

        $client = new Client();

        try {
            $response = $client->get(config('bexio.api_base_url') . '/' . $endpoint, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $user->access_token,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true);

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            Log::error("Bexio API error for {$endpoint}: {$statusCode} - {$responseBody}");

            return response()->json([
                'error' => 'API request failed',
                'message' => 'Failed to fetch data from Bexio'
            ], 500);
        }
    }

    public function getCustomers()
    {
        $user = Auth::user();

        if (!$user || !$user->access_token) {
            return response()->json(['error' => 'No access token available'], 401);
        }

        $data = $this->makeApiRequest('3.0/contact');

        if (is_array($data)) {
            // Filter only customers (not suppliers)
            $customers = array_filter($data, function($contact) {
                return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 1;
            });

            Log::info("Loaded " . count($customers) . " customers from Bexio for user {$user->id}");
            return response()->json(array_values($customers));
        }

        // If API request failed, return mock data as fallback
        Log::warning("Bexio API failed, returning mock customers for user {$user->id}");
        return response()->json([
            [
                'id' => 1,
                'name_1' => 'John Doe',
                'mail' => '<EMAIL>',
                'contact_type_id' => 1
            ],
            [
                'id' => 2,
                'name_1' => 'Jane Smith',
                'mail' => '<EMAIL>',
                'contact_type_id' => 1
            ],
            [
                'id' => 3,
                'name_1' => 'Acme Corporation',
                'mail' => '<EMAIL>',
                'contact_type_id' => 1
            ]
        ]);
    }

    public function getUnits()
    {
        $user = Auth::user();

        if (!$user || !$user->access_token) {
            return response()->json(['error' => 'No access token available'], 401);
        }

        $data = $this->makeApiRequest('2.0/unit');

        if (is_array($data)) {
            Log::info("Loaded " . count($data) . " units from Bexio for user {$user->id}");
            return response()->json($data);
        }

        // If API request failed, return mock data as fallback
        Log::warning("Bexio API failed, returning mock units for user {$user->id}");
        return response()->json([
            ['id' => 1, 'name' => 'Pieces'],
            ['id' => 2, 'name' => 'Hours'],
            ['id' => 3, 'name' => 'Days'],
            ['id' => 4, 'name' => 'Months'],
            ['id' => 5, 'name' => 'Years']
        ]);
    }

    public function getTaxes()
    {
        $user = Auth::user();

        if (!$user || !$user->access_token) {
            return response()->json(['error' => 'No access token available'], 401);
        }

        $data = $this->makeApiRequest('2.0/tax');

        if (is_array($data)) {
            Log::info("Loaded " . count($data) . " taxes from Bexio for user {$user->id}");
            return response()->json($data);
        }

        // If API request failed, return mock data as fallback
        Log::warning("Bexio API failed, returning mock taxes for user {$user->id}");
        return response()->json([
            ['id' => 1, 'display_name' => '7.7% VAT', 'percentage' => 7.7],
            ['id' => 2, 'display_name' => '2.5% VAT', 'percentage' => 2.5],
            ['id' => 3, 'display_name' => '0% VAT', 'percentage' => 0],
            ['id' => 4, 'display_name' => '3.7% VAT', 'percentage' => 3.7]
        ]);
    }

    public function createCustomer(Request $request)
    {
        $user = Auth::user();

        if (!$user || !$user->access_token) {
            return response()->json(['error' => 'No access token available'], 401);
        }

        $customerData = $request->input('customer');

        // Prepare data for Bexio API
        $bexioData = [
            'contact_type_id' => $customerData['type'] ?? 1, // 1 = Customer, 2 = Supplier
            'name_1' => $customerData['name'],
            'mail' => $customerData['email'],
            'address' => $customerData['address'] ?? '',
            'city' => $customerData['city'] ?? '',
            'postcode' => $customerData['postcode'] ?? '',
        ];

        $client = new Client();

        try {
            $response = $client->post(config('bexio.api_base_url') . '/2.0/contact', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $user->access_token,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'json' => $bexioData
            ]);

            $createdCustomer = json_decode($response->getBody()->getContents(), true);

            return response()->json($createdCustomer);

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            Log::error("Bexio API error creating customer: {$statusCode} - {$responseBody}");

            return response()->json([
                'error' => 'Failed to create customer',
                'message' => 'Could not create customer in Bexio'
            ], 500);
        }
    }
}
