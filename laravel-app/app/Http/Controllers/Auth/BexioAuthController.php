<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use App\Models\Subscription;
use App\Services\BexioServiceInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Ju<PERSON>jett\OpenIDConnectClient;

class BexioAuthController extends Controller
{
    public function __construct(
        private BexioServiceInterface $bexioService
    ) {}

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function redirect()
    {
        try {
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            if (!$clientId || !$clientSecret) {
                Log::error('Bexio client credentials not configured');
                return redirect()->route('login')->withErrors(['auth' => 'Bexio client credentials not configured']);
            }

            $oidc = new OpenIDConnectClient(
                'https://auth.bexio.com/realms/bexio',
                $clientId,
                $clientSecret
            );

            $oidc->setRedirectURL(config('bexio.redirect_uri'));
            $oidc->addScope(['openid', 'profile', 'email', 'accounting']);

            $oidc->authenticate();

        } catch (\Exception $e) {
            Log::error('Bexio OAuth redirect failed: ' . $e->getMessage());
            return redirect()->route('login')->withErrors(['auth' => 'Authentication failed']);
        }
    }

    public function callback()
    {
        // Check for OAuth errors first - matching PHP original
        if (request('error')) {
            Log::error('OAuth error received: ' . request('error'));
            return redirect('/login')->withErrors([
                'bexio' => 'OAuth error: ' . request('error')
            ]);
        }

        try {
            // Use OpenIDConnectClient like PHP original
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            if (!$clientId || !$clientSecret) {
                Log::error('Bexio client credentials not configured');
                return redirect()->route('login')->withErrors(['auth' => 'Bexio client credentials not configured']);
            }

            $oidc = new OpenIDConnectClient(
                'https://auth.bexio.com/realms/bexio',
                $clientId,
                $clientSecret
            );

            $oidc->setRedirectURL(config('bexio.redirect_uri'));
            $oidc->addScope(['openid', 'profile', 'email', 'accounting']);

            // Authenticate and get tokens
            $oidc->authenticate();

            $accessToken = $oidc->getAccessToken();
            $refreshToken = $oidc->getRefreshToken();
            $expiresIn = 3600; // bexio default (1h)

            if (!$accessToken) {
                return redirect('/login')->withErrors([
                    'bexio' => 'Failed to obtain access token'
                ]);
            }

            // Get user info from OIDC
            $userinfo = $oidc->requestUserInfo();

            // Get company profile directly from Bexio API - matching PHP original
            $ch = curl_init('https://api.bexio.com/3.0/company_profile');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $accessToken,
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $profileResponse = curl_exec($ch);
            curl_close($ch);

            $profiles = json_decode($profileResponse, true);
            $profile = is_array($profiles) && isset($profiles[0]) ? $profiles[0] : [];

            // Extract data like PHP original
            $companyId = $userinfo->company_id ?? '';
            $companyName = $profile['name'] ?? '';
            $address = $profile['address'] ?? '';
            $zip = $profile['postcode'] ?? '';
            $city = $profile['city'] ?? '';
            $email = $profile['mail'] ?? '';
            $phone = $profile['phone_fixed'] ?? '';

            // Contact person from userinfo
            $contactName = trim(($userinfo->given_name ?? '') . ' ' . ($userinfo->family_name ?? ''));

            $companyData = [
                'id' => $companyId,
                'name' => $companyName,
                'address' => $address,
                'postcode' => $zip,
                'city' => $city,
                'mail' => $email,
                'phone_fixed' => $phone,
                'contact_name' => $contactName,
            ];

            $userData = [
                'id' => $userinfo->sub ?? '',
                'name' => trim(($userinfo->given_name ?? '') . ' ' . ($userinfo->family_name ?? '')),
                'email' => $userinfo->email ?? '',
                'given_name' => $userinfo->given_name ?? '',
                'family_name' => $userinfo->family_name ?? '',
            ];

            $tokenData = [
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'expires_in' => $expiresIn,
            ];

            return DB::transaction(function () use ($tokenData, $userData, $companyData, $refreshToken) {
                // Create or update organization - matching PHP original
                $organization = Organization::updateOrCreate(
                    ['bexio_org_id' => $companyData['id']],
                    [
                        'name' => $companyData['name'],
                        'email' => $companyData['mail'],
                        'address' => $companyData['address'],
                        'zip' => $companyData['postcode'],
                        'city' => $companyData['city'],
                        'contact_name' => $companyData['contact_name'],
                        'phone' => $companyData['phone_fixed'],
                        'refresh_token' => $refreshToken,
                        'bexio_company_profile' => $companyData,
                        'subscription_status' => 'trial',
                        'subscription_start' => now()->toDateString(),
                    ]
                );

                // Set trial period for new organizations
                if ($organization->wasRecentlyCreated) {
                    $organization->update([
                        'trial_ends_at' => now()->addMonths(3),
                        'status' => 'trial'
                    ]);
                }

                // Create or update user - matching PHP original logic
                $user = User::updateOrCreate(
                    [
                        'bexio_id' => $userData['id'],
                        'organization_id' => $organization->id
                    ],
                    [
                        'name' => $userData['name'],
                        'email' => $userData['email'],
                        'access_token' => $tokenData['access_token'],
                        'refresh_token' => $tokenData['refresh_token'] ?? null,
                        'token_expires_at' => isset($tokenData['expires_in'])
                            ? now()->addSeconds($tokenData['expires_in'])
                            : now()->addHour(),
                        'refresh_token_rotated_at' => now(),
                        'last_login_at' => now(),
                        'bexio_user_profile' => $userData,
                        'bexio_company_id' => $companyData['id'],
                        'bexio_user_id' => $userData['id'],
                    ]
                );

                // Create subscription if it doesn't exist
                if (!$organization->subscription) {
                    Subscription::create([
                        'organization_id' => $organization->id,
                        'plan_type' => 'monthly',
                        'price' => 29.00, // Default monthly price
                        'status' => 'trial',
                        'trial_ends_at' => $organization->trial_ends_at,
                    ]);
                }

                Auth::login($user);

                // Send welcome email for new organizations
                if ($organization->wasRecentlyCreated) {
                    $this->sendWelcomeEmail($user, $organization);
                }

                return redirect('/dashboard');
            });

        } catch (\Exception $e) {
            Log::error('Bexio authentication failed: ' . $e->getMessage());
            return redirect('/login')->withErrors([
                'bexio' => 'Failed to authenticate with Bexio'
            ]);
        }
    }

    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }



    private function sendWelcomeEmail($user, $organization)
    {
        // TODO: Implement welcome email sending
        Log::info("Welcome email should be sent to {$user->email} for organization {$organization->name}");
    }


}
