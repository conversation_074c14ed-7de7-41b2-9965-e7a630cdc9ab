<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Invoice;
use App\Models\RecurringTemplate;
use App\Services\BexioServiceInterface;

class InvoiceController extends Controller
{
    public function __construct(
        private BexioServiceInterface $bexioService
    ) {}

    public function create()
    {
        return view('invoices.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'document_nr' => 'required|string|max:255',
            'contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255',
            'contact_address' => 'nullable|string',
            'total' => 'required|numeric|min:0',
            'status' => 'required|in:draft,sent,paid,cancelled',
            'is_recurring' => 'boolean',
            'recurring_interval' => 'nullable|in:daily,weekly,monthly',
            'next_run_date' => 'nullable|date|after:today'
        ]);

        $user = Auth::user();

        // Create invoice
        $invoice = Invoice::create([
            'user_id' => $user->id,
            'bexio_id' => 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
            'document_nr' => $request->document_nr,
            'contact_info' => [
                'name' => $request->contact_name,
                'email' => $request->contact_email,
                'address' => $request->contact_address
            ],
            'total' => $request->total,
            'status' => $request->status,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurring_settings' => $request->is_recurring ? [
                'interval' => $request->recurring_interval,
                'next_charge' => $request->next_run_date
            ] : null
        ]);

        // Create recurring template if needed
        if ($request->boolean('is_recurring') && $request->recurring_interval && $request->next_run_date) {
            RecurringTemplate::create([
                'user_id' => $user->id,
                'invoice_id' => $invoice->id,
                'interval' => $request->recurring_interval,
                'next_run' => $request->next_run_date
            ]);
        }

        return redirect()->route('dashboard')->with('success', 'Invoice created successfully!');
    }

    public function index()
    {
        $user = Auth::user();
        $invoices = Invoice::where('user_id', $user->id)
            ->with('recurringTemplate')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('invoices.index', compact('invoices'));
    }

    public function show(Invoice $invoice)
    {
        // Ensure user can only view their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        return view('invoices.show', compact('invoice'));
    }

    public function edit(Invoice $invoice)
    {
        // Ensure user can only edit their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        return view('invoices.edit', compact('invoice'));
    }

    public function update(Request $request, Invoice $invoice)
    {
        // Ensure user can only update their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'document_nr' => 'required|string|max:255',
            'contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255',
            'contact_address' => 'nullable|string',
            'total' => 'required|numeric|min:0',
            'status' => 'required|in:draft,sent,paid,cancelled',
        ]);

        $invoice->update([
            'document_nr' => $request->document_nr,
            'contact_info' => [
                'name' => $request->contact_name,
                'email' => $request->contact_email,
                'address' => $request->contact_address
            ],
            'total' => $request->total,
            'status' => $request->status,
        ]);

        return redirect()->route('invoices.show', $invoice)->with('success', 'Invoice updated successfully!');
    }

    public function destroy(Invoice $invoice)
    {
        // Ensure user can only delete their own invoices
        if ($invoice->user_id !== Auth::id()) {
            abort(403);
        }

        $invoice->delete();

        return redirect()->route('invoices.index')->with('success', 'Invoice deleted successfully!');
    }
}
