<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SubscriptionController extends Controller
{
    public function requestActivation(Request $request)
    {
        $user = Auth::user();
        $organization = $user->organization;

        if (!$organization) {
            return redirect()->route('login')->withErrors(['error' => 'No organization found']);
        }

        $planType = $request->input('plan_type', 'monthly');
        $price = $planType === 'yearly' ? 300.00 : 29.00;

        // Log the activation request
        Log::info("Subscription activation requested", [
            'user_id' => $user->id,
            'organization_id' => $organization->id,
            'organization_name' => $organization->name,
            'plan_type' => $planType,
            'price' => $price,
            'user_email' => $user->email,
        ]);

        // Send activation email to finance team
        $this->sendActivationRequestEmail($user, $organization, $planType, $price);

        // Update subscription request status
        if ($organization->subscription) {
            $organization->subscription->update([
                'plan_type' => $planType,
                'price' => $price,
                'notes' => 'Activation requested at ' . now()->format('Y-m-d H:i:s'),
            ]);
        }

        return redirect()->route('dashboard')->with('info',
            'Ihre Aktivierungsanfrage wurde gesendet. Sie erhalten in Kürze eine E-Mail mit den Zahlungsdetails.'
        );
    }

    private function sendActivationRequestEmail($user, $organization, $planType, $price)
    {
        $subject = "Kim Rebill - Aktivierungsanfrage von {$organization->name}";

        $message = "
Neue Aktivierungsanfrage für Kim Rebill:

Organisation: {$organization->name}
Benutzer: {$user->name} ({$user->email})
Plan: " . ucfirst($planType) . "
Preis: CHF {$price}" . ($planType === 'yearly' ? '/Jahr' : '/Monat') . "

Bexio Organisation ID: {$organization->bexio_organization_id}
Angefragt am: " . now()->format('d.m.Y H:i:s') . "

Bitte aktivieren Sie das Abonnement nach Zahlungseingang.

---
Kim Rebill System
        ";

        // TODO: Implement actual email sending with SMTP2GO
        // For now, just log the email content
        Log::info("Activation request email", [
            'to' => '<EMAIL>',
            'subject' => $subject,
            'message' => $message,
        ]);

        // In production, use:
        // Mail::raw($message, function ($mail) use ($subject) {
        //     $mail->to('<EMAIL>')
        //          ->subject($subject)
        //          ->from('<EMAIL>', 'Kim Rebill')
        //          ->replyTo('<EMAIL>');
        // });
    }
}
