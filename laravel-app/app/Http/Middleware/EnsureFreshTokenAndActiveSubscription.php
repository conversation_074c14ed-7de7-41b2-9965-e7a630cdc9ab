<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Helpers\BexioAuth;

class EnsureFreshTokenAndActiveSubscription
{
    /**
     * Handle an incoming request - equivalent to requireLoginAndFreshToken()
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware for OAuth callback routes
        if ($request->is('auth/*')) {
            return $next($request);
        }

        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user has organization
        if (!$user->organization) {
            Auth::logout();
            return redirect()->route('login')->withErrors(['auth' => 'No organization found']);
        }

        $organization = $user->organization;

        // Check subscription status - equivalent to subscription_status check
        if ($organization->status === 'inactive') {
            return response()->view('subscription.inactive', [
                'organization' => $organization,
                'user' => $user
            ], 403);
        }

        // Check if trial expired and no active subscription
        if ($organization->isTrial() && $organization->isTrialExpired()) {
            return response()->view('subscription.trial-expired', [
                'organization' => $organization,
                'user' => $user
            ], 403);
        }

        // Auto-refresh token if expiring within configured threshold - matching PHP original
        if ($user->token_expires_at &&
            $user->token_expires_at->diffInSeconds(now()) < config('bexio.token_refresh_threshold')) {

            $authResult = BexioAuth::requireLoginAndFreshToken();
            if ($authResult) {
                return $authResult; // Redirect if refresh failed
            }
        }

        return $next($request);
    }


}
