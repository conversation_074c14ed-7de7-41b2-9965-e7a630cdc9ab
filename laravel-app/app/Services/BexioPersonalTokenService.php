<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BexioPersonalTokenService implements BexioServiceInterface
{
    private string $personalToken;
    private string $apiBaseUrl;

    public function __construct()
    {
        $this->personalToken = config('bexio.personal_token');
        $this->apiBaseUrl = config('bexio.api_base_url', 'https://api.bexio.com');
    }

    public function getAuthUrl(): string
    {
        // Personal token doesn't need OAuth, redirect directly to callback
        return route('bexio.personal-token-login');
    }

    public function handleCallback(string $code): array
    {
        // For personal token, we don't need to exchange code for token
        return [
            'access_token' => $this->personalToken,
            'token_type' => 'Bearer',
            'expires_in' => 3600 * 24 * 365, // 1 year (personal tokens don't expire)
        ];
    }

    public function getAccessToken(): ?string
    {
        return $this->personalToken;
    }

    public function setAccessToken(string $token): void
    {
        $this->personalToken = $token;
    }

    public function refreshToken(): array
    {
        // Personal tokens don't need refresh
        return [
            'access_token' => $this->personalToken,
            'expires_in' => 3600 * 24 * 365,
        ];
    }

    public function getUserData(): array
    {
        return $this->bexioRequest('GET', '2.0/users/me');
    }

    public function getCompanyProfile(): array
    {
        return $this->bexioRequest('GET', '2.0/company_profile');
    }

    public function getInvoices(): array
    {
        return $this->bexioRequest('GET', '3.0/kb_invoice');
    }

    public function getRecurringInvoices(): array
    {
        // Get recurring bills from Bexio
        return $this->bexioRequest('GET', '3.0/recurring_bill');
    }

    public function getDrafts(): array
    {
        // Get draft invoices
        $invoices = $this->bexioRequest('GET', '3.0/kb_invoice');
        return array_filter($invoices, function($invoice) {
            return isset($invoice['kb_item_status_id']) && $invoice['kb_item_status_id'] == 1; // Draft status
        });
    }

    public function getContacts(): array
    {
        return $this->bexioRequest('GET', '3.0/contact');
    }

    public function getContact($id): array
    {
        return $this->bexioRequest('GET', "3.0/contact/{$id}");
    }

    public function getUnits(): array
    {
        return $this->bexioRequest('GET', '2.0/units');
    }

    public function getTaxes(): array
    {
        return $this->bexioRequest('GET', '2.0/tax');
    }

    public function getCurrencies(): array
    {
        return $this->bexioRequest('GET', '2.0/currency');
    }

    public function createCustomer(array $data): array
    {
        return $this->bexioRequest('POST', '3.0/contact', $data);
    }

    public function createInvoice(array $data): array
    {
        return $this->bexioRequest('POST', '3.0/kb_invoice', $data);
    }

    public function updateInvoice($id, array $data): array
    {
        return $this->bexioRequest('POST', "3.0/kb_invoice/{$id}", $data);
    }

    public function restartRecurringBilling($id): array
    {
        return $this->bexioRequest('POST', "3.0/recurring_bill/{$id}/restart");
    }

    public function deleteRecurringBilling($id): array
    {
        return $this->bexioRequest('DELETE', "3.0/recurring_bill/{$id}");
    }

    public function pauseRecurringBilling($id): array
    {
        return $this->bexioRequest('POST', "3.0/recurring_bill/{$id}/pause");
    }

    private function bexioRequest(string $method, string $path, ?array $data = null): array
    {
        if (!$this->personalToken) {
            Log::error('Bexio personal token not configured');
            return [];
        }

        $url = "{$this->apiBaseUrl}/{$path}";
        
        $request = Http::withToken($this->personalToken)
            ->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]);

        try {
            $response = match(strtoupper($method)) {
                'GET' => $request->get($url),
                'POST' => $request->post($url, $data),
                'PUT' => $request->put($url, $data),
                'DELETE' => $request->delete($url),
                default => throw new \InvalidArgumentException("Unsupported HTTP method: {$method}")
            };

            if ($response->successful()) {
                $result = $response->json();
                Log::info("Bexio API success: {$method} {$path}", [
                    'status' => $response->status(),
                    'data_count' => is_array($result) ? count($result) : 'single'
                ]);
                return $result;
            }

            Log::error("Bexio API error: {$response->status()} - {$response->body()}", [
                'method' => $method,
                'path' => $path,
                'url' => $url
            ]);
            return [];
        } catch (\Exception $e) {
            Log::error("Bexio API exception: " . $e->getMessage(), [
                'method' => $method,
                'path' => $path,
                'url' => $url
            ]);
            return [];
        }
    }
}
