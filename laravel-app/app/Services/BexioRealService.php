<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BexioRealService implements BexioServiceInterface
{
    protected string $clientId;
    protected string $clientSecret;
    protected string $redirectUri;
    protected ?string $accessToken = null;

    public function __construct()
    {
        $this->clientId = config('services.bexio.client_id');
        $this->clientSecret = config('services.bexio.client_secret');
        $this->redirectUri = config('services.bexio.redirect');
    }

    public function getAuthUrl(): string
    {
        $scopes = config('bexio.scopes', [
            'openid',
            'profile',
            'email',
            'offline_access',
            'kb_invoice_edit',
            'contact_edit',
            'company_profile'
        ]);

        return 'https://office.bexio.com/oauth/authorize?' . http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUri,
            'response_type' => 'code',
            'scope' => implode(' ', $scopes)
        ]);
    }

    public function handleCallback(string $code): array
    {
        $response = Http::asForm()->post('https://office.bexio.com/oauth/token', [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $this->redirectUri
        ]);

        $data = $response->json();
        $this->accessToken = $data['access_token'] ?? null;

        return $data;
    }

    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    public function setAccessToken(string $token): void
    {
        $this->accessToken = $token;
    }

    public function refreshToken(): array
    {
        // Implementation for token refresh
        return [];
    }

    public function getUserData(): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $response = Http::withToken($this->accessToken)
            ->get('https://office.bexio.com/api/v1/users/me');

        return $response->json();
    }

    public function getCompanyProfile(): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $response = Http::withToken($this->accessToken)
            ->get('https://api.bexio.com/2.0/company_profile');

        if ($response->successful()) {
            return $response->json();
        }

        return [];
    }

    public function getInvoices(): array
    {
        return $this->bexioRequest('GET', '3.0/kb_invoice');
    }

    public function getContacts(): array
    {
        return $this->bexioRequest('GET', '3.0/contact');
    }

    public function getContact($id): array
    {
        return $this->bexioRequest('GET', "3.0/contact/{$id}");
    }

    public function getUnits(): array
    {
        return $this->bexioRequest('GET', '2.0/units');
    }

    public function getTaxes(): array
    {
        return $this->bexioRequest('GET', '2.0/tax');
    }

    public function getCurrencies(): array
    {
        return $this->bexioRequest('GET', '2.0/currency');
    }

    public function createCustomer(array $data): array
    {
        return $this->bexioRequest('POST', '3.0/contact', $data);
    }

    public function createInvoice(array $data): array
    {
        return $this->bexioRequest('POST', '3.0/kb_invoice', $data);
    }

    public function updateInvoice($id, array $data): array
    {
        return $this->bexioRequest('POST', "3.0/kb_invoice/{$id}", $data);
    }

    public function restartRecurringBilling($id): array
    {
        return $this->bexioRequest('POST', "3.0/recurring_bill/{$id}/restart");
    }

    public function deleteRecurringBilling($id): array
    {
        return $this->bexioRequest('DELETE', "3.0/recurring_bill/{$id}");
    }

    public function pauseRecurringBilling($id): array
    {
        return $this->bexioRequest('POST', "3.0/recurring_bill/{$id}/pause");
    }

    private function bexioRequest(string $method, string $path, ?array $data = null): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $url = "https://api.bexio.com/{$path}";

        $request = Http::withToken($this->accessToken)
            ->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]);

        try {
            $response = match(strtoupper($method)) {
                'GET' => $request->get($url),
                'POST' => $request->post($url, $data),
                'PUT' => $request->put($url, $data),
                'DELETE' => $request->delete($url),
                default => throw new \InvalidArgumentException("Unsupported HTTP method: {$method}")
            };

            if ($response->successful()) {
                return $response->json();
            }

            Log::error("Bexio API error: {$response->status()} - {$response->body()}");
            return [];
        } catch (\Exception $e) {
            Log::error("Bexio API exception: " . $e->getMessage());
            return [];
        }
    }

    public function getRecurringInvoices(): array
    {
        // Implementation for real Bexio API
        // For now, return empty array
        return [];
    }

    public function getDrafts(): array
    {
        // Implementation for real Bexio API
        // For now, return empty array
        return [];
    }
}
