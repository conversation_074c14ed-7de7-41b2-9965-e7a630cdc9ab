<?php

namespace App\Services;

interface BexioServiceInterface
{
    public function getAuthUrl(): string;
    public function handleCallback(string $code): array;
    public function getAccessToken(): ?string;
    public function setAccessToken(string $token): void;
    public function refreshToken(): array;
    public function getUserData(): array;
    public function getCompanyProfile(): array;
    public function getInvoices(): array;
    public function getRecurringInvoices(): array;
    public function getDrafts(): array;

    // New Bexio API methods
    public function getContacts(): array;
    public function getContact($id): array;
    public function getUnits(): array;
    public function getTaxes(): array;
    public function getCurrencies(): array;
    public function createCustomer(array $data): array;
    public function createInvoice(array $data): array;
    public function updateInvoice($id, array $data): array;
    public function restartRecurringBilling($id): array;
    public function deleteRecurringBilling($id): array;
    public function pauseRecurringBilling($id): array;
}
