<?php

namespace App\Services;

use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use <PERSON><PERSON>\Socialite\Two\User;

class BexioProvider extends AbstractProvider implements ProviderInterface
{
    protected $scopes = ['openid', 'profile', 'email'];
    protected $scopeSeparator = ' ';

    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase(
            'https://oauth.bexio.com/authorize',
            $state
        );
    }

    protected function getTokenUrl()
    {
        return 'https://oauth.bexio.com/token';
    }

    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get(
            'https://api.bexio.com/2.0/users/me',
            [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                ],
            ]
        );

        return json_decode($response->getBody(), true);
    }

    protected function mapUserToObject(array $user)
    {
        return (new User())->setRaw($user)->map([
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
        ]);
    }
}
