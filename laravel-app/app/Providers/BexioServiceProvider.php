<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\BexioServiceInterface;
use App\Services\BexioRealService;
use App\Services\BexioMockService;

class BexioServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(BexioServiceInterface::class, function ($app) {
            return config('services.bexio.mode') === 'demo'
                ? new BexioMockService()
                : new BexioRealService();
        });
    }

    public function boot()
    {
        // Register routes or other bootstrapping if needed
    }
}
