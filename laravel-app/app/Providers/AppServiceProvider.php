<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\BexioServiceInterface;
use App\Services\BexioRealService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(BexioServiceInterface::class, function () {
            return new BexioRealService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
