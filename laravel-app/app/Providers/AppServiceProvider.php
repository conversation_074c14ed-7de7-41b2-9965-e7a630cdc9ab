<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\BexioServiceInterface;
use App\Services\BexioMockService;
use App\Services\BexioRealService;
use App\Services\BexioPersonalTokenService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(BexioServiceInterface::class, function ($app) {
            $mode = config('bexio.mode', 'demo');

            return match($mode) {
                'real' => new BexioRealService(),
                'token' => new BexioPersonalTokenService(),
                default => new BexioMockService(),
            };
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
