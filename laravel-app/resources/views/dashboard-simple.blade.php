@extends('layouts.rebill')

@php
    $title = "Dashboard";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <h1>Dashboard</h1>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Invoices</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/create_invoice" class="btn btn-outline-primary">
                                    ➕ Create New Invoice
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/edit_invoice" class="btn btn-outline-secondary">
                                    ✏️ Edit Invoice
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            @if(Auth::user()->is_admin)
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Administration</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/admin" class="btn btn-outline-warning">
                                    🔧 Admin Panel
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">User Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> {{ Auth::user()->name }}</p>
                                <p><strong>Email:</strong> {{ Auth::user()->email }}</p>
                                <p><strong>Role:</strong>
                                    @if(Auth::user()->is_admin)
                                        <span class="badge bg-warning">Administrator</span>
                                    @else
                                        <span class="badge bg-secondary">User</span>
                                    @endif
                                </p>
                            </div>
                            <div class="col-md-6">
                                @if(Auth::user()->organization)
                                <p><strong>Organization:</strong> {{ Auth::user()->organization->name }}</p>
                                <p><strong>Subscription:</strong>
                                    @if(Auth::user()->organization->subscription_status === 'trial')
                                        <span class="badge bg-info">Trial</span>
                                    @elseif(Auth::user()->organization->subscription_status === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
