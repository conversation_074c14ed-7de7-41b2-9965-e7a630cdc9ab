@extends('layouts.rebill')

@php
    $title = "Dashboard";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <h1>Dashboard</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Rechnungen</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/create_invoice" class="btn btn-outline-primary">
                                    ➕ Neue Rechnung erstellen
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/edit_invoice" class="btn btn-outline-secondary">
                                    ✏️ Rechnung bearbeiten
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            @if(Auth::user()->is_admin)
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Administration</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/admin" class="btn btn-outline-warning">
                                    🔧 Adminbereich
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            @endif
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Benutzerinformationen</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> {{ Auth::user()->name }}</p>
                                <p><strong>E-Mail:</strong> {{ Auth::user()->email }}</p>
                                <p><strong>Rolle:</strong> 
                                    @if(Auth::user()->is_admin)
                                        <span class="badge bg-warning">Administrator</span>
                                    @else
                                        <span class="badge bg-secondary">Benutzer</span>
                                    @endif
                                </p>
                            </div>
                            <div class="col-md-6">
                                @if(Auth::user()->organization)
                                <p><strong>Organisation:</strong> {{ Auth::user()->organization->name }}</p>
                                <p><strong>Abonnement:</strong> 
                                    @if(Auth::user()->organization->subscription_status === 'trial')
                                        <span class="badge bg-info">Testversion</span>
                                    @elseif(Auth::user()->organization->subscription_status === 'active')
                                        <span class="badge bg-success">Aktiv</span>
                                    @else
                                        <span class="badge bg-danger">Inaktiv</span>
                                    @endif
                                </p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
