<!doctype html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $title ?? 'reBill' }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    @php
        $user = Auth::user();
        $organization = $user ? $user->organization : null;
        $orgName = $organization ? $organization->name : 'einfach wiederkehrende Rechnungen';
        $logoUrl = $organization && $organization->bexio_org_id 
            ? "https://office.bexio.com/img/{$organization->bexio_org_id}/companyLogo.png"
            : asset('assets/reBill.png');
        $isWelcomePage = request()->routeIs('login') || request()->routeIs('welcome');
    @endphp

    <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ $user ? route('dashboard') : '/' }}">
                <img src="{{ $logoUrl }}" alt="Logo" style="height:32px;width:auto;margin-right:12px;" />
                <span>{{ $orgName }}</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainnav">
                <span class="navbar-toggler-icon"></span>
            </button>

            @if(!$isWelcomePage && $user)
            <div class="collapse navbar-collapse" id="mainnav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" 
                           href="{{ route('dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('invoice-templates*') ? 'active' : '' }}" 
                           href="/invoice-templates">Rechnungsvorlagen</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('settings*') ? 'active' : '' }}" 
                           href="/settings">Einstellungen</a>
                    </li>
                    @if($user->is_admin)
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin*') ? 'active' : '' }}" 
                           href="/admin">Admin</a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger">Logout</button>
                        </form>
                    </li>
                </ul>
            </div>
            @endif
        </div>
    </nav>

    <div class="container">
        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                @if($errors->has('bexio'))
                    {{ $errors->first('bexio') }}
                @elseif($errors->has('auth'))
                    {{ $errors->first('auth') }}
                @else
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @yield('content')
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    @stack('scripts')
</body>
</html>
