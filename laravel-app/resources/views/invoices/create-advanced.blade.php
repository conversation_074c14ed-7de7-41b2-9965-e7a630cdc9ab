@extends('layouts.rebill')

@php
    $title = "Neue Rechnung erstellen";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-receipt me-2"></i>
                <h1 class="mb-0">Neue Rechnung erstellen</h1>
            </div>
            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Zurück
            </a>
        </div>

        <form id="invoice-form" action="{{ route('invoices.store') }}" method="POST">
            @csrf

            <!-- Invoice Details Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Rechnungsdetails</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rebill_title" class="form-label">Titel in reBill <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="rebill_title" name="rebill_title"
                                       placeholder="z.B. Bauleistungen..." required>
                                <div class="form-text">Ein kurzer Titel zur Beschreibung Ihrer Rechnung</div>
                            </div>
                            <div class="mb-3">
                                <label for="rebill_description" class="form-label">Beschreibung in reBill</label>
                                <textarea class="form-control" id="rebill_description" name="rebill_description" rows="3"
                                          placeholder="z.B. Laufende Entwicklungsdienstleistungen..."></textarea>
                                <div class="form-text">Beschreiben Sie die Produkte/Dienstleistungen</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bexio_title" class="form-label">Titel in Bexio</label>
                                <input type="text" class="form-control" id="bexio_title" name="bexio_title"
                                       placeholder="z.B. Bauleistungen...">
                                <div class="form-text">Titel für die Bexio-Rechnung</div>
                            </div>
                            <div class="mb-3">
                                <label for="bexio_reference" class="form-label">Referenz in Bexio</label>
                                <textarea class="form-control" id="bexio_reference" name="bexio_reference" rows="3"
                                          placeholder="z.B. Laufende Entwicklungsdienstleistungen..."></textarea>
                                <div class="form-text">Referenz für die Bexio-Rechnung</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Selection Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Kunde auswählen</h5>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="customer_type" id="existing_customer" value="existing" checked>
                            <label class="btn btn-outline-primary" for="existing_customer">Bestehend</label>

                            <input type="radio" class="btn-check" name="customer_type" id="new_customer" value="new">
                            <label class="btn btn-outline-primary" for="new_customer">Neu</label>
                        </div>
                    </div>
                    <small class="text-muted">Wählen Sie einen bestehenden Kunden oder erstellen Sie einen neuen</small>
                </div>
                <div class="card-body">
                    <!-- Existing Customer -->
                    <div id="existing-customer-section">
                        <div class="mb-3">
                            <label for="customer_id" class="form-label">Kunde <span class="text-danger">*</span></label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">Kunde auswählen...</option>
                                <!-- Options will be loaded via AJAX -->
                            </select>
                        </div>
                    </div>

                    <!-- New Customer -->
                    <div id="new-customer-section" style="display: none;">
                        <div class="p-3 bg-light rounded">
                            <h6 class="fw-bold">Neuer Kunde</h6>
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_name" class="form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="new_customer_name" name="new_customer_name"
                                               placeholder="Max Mustermann">
                                    </div>
                                    <div class="mb-3">
                                        <label for="new_customer_email" class="form-label">E-Mail <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="new_customer_email" name="new_customer_email"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_type" class="form-label">Kundentyp <span class="text-danger">*</span></label>
                                        <select class="form-select" id="new_customer_type" name="new_customer_type">
                                            <option value="1">Unternehmen</option>
                                            <option value="2">Privatperson</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new_customer_address" class="form-label">Adresse</label>
                                <input type="text" class="form-control" id="new_customer_address" name="new_customer_address"
                                       placeholder="Musterstraße 123">
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_city" class="form-label">Stadt</label>
                                        <input type="text" class="form-control" id="new_customer_city" name="new_customer_city"
                                               placeholder="Berlin">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_postcode" class="form-label">PLZ</label>
                                        <input type="text" class="form-control" id="new_customer_postcode" name="new_customer_postcode"
                                               placeholder="10115">
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-success" id="create-customer-btn">
                                <i class="fas fa-plus me-1"></i> Kunde erstellen
                            </button>
                            <small class="d-block text-muted mt-2">Sie können weitere Informationen im Bexio-Dashboard hinzufügen</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Abrechnung</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="billing_period" class="form-label">Abrechnungszeitraum <span class="text-danger">*</span></label>
                                <select class="form-select" id="billing_period" name="billing_period" required>
                                    <option value="monthly">Monatlich</option>
                                    <option value="yearly">Jährlich</option>
                                </select>
                                <div class="form-text">Ihr Kunde wird in diesem Zyklus abgerechnet</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Startdatum <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       min="{{ date('Y-m-d') }}" value="{{ date('Y-m-d') }}" required>
                                <div class="form-text">Rechnungen werden ab diesem Datum an Ihren Kunden gesendet</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Positionen</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 30%">Name <small class="text-muted">(mit Variablen: [year], [month], [nextyear], [nextmonth])</small></th>
                                    <th style="width: 12%">Menge</th>
                                    <th style="width: 15%">Einheit</th>
                                    <th style="width: 15%">Einzelpreis</th>
                                    <th style="width: 15%">Steuer</th>
                                    <th style="width: 10%">Gesamt</th>
                                    <th style="width: 3%"></th>
                                </tr>
                            </thead>
                            <tbody id="items-container">
                                <!-- Items will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>

                    <button type="button" class="btn btn-outline-primary" id="add-item-btn">
                        <i class="fas fa-plus me-1"></i> Position hinzufügen
                    </button>

                    <hr>
                    <div class="text-end">
                        <h5>Gesamt: <span id="total-amount">0.00</span> CHF</h5>
                    </div>
                </div>
            </div>

            <!-- Tax Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Steuer</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="tax_status" class="form-label">Steuerstatus <span class="text-danger">*</span></label>
                        <select class="form-select" id="tax_status" name="tax_status" required>
                            <option value="0">Inklusive Steuern</option>
                            <option value="1">Exklusive Steuern</option>
                            <option value="2">Steuerbefreit</option>
                        </select>
                        <div class="form-text">Wählen Sie den Steuerstatus dieser Rechnung</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-receipt me-1"></i> Rechnung erstellen
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="save-draft-btn">
                            <i class="fas fa-save me-1"></i> Als Entwurf speichern
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Item Template (hidden) -->
<template id="item-template">
    <tr class="item-row">
        <td>
            <textarea class="form-control item-name" name="items[INDEX][name]" rows="2"
                      placeholder="Positionsname" required></textarea>
        </td>
        <td>
            <input type="number" class="form-control item-quantity" name="items[INDEX][quantity]"
                   min="0" step="0.01" value="1" required>
        </td>
        <td>
            <select class="form-select item-unit" name="items[INDEX][unit_id]" required>
                <option value="">Einheit wählen...</option>
                <!-- Units will be loaded via AJAX -->
            </select>
        </td>
        <td>
            <input type="number" class="form-control item-price" name="items[INDEX][unit_price]"
                   min="0" step="0.01" value="0" required>
        </td>
        <td>
            <select class="form-select item-tax" name="items[INDEX][tax_id]" required>
                <option value="">Steuer wählen...</option>
                <!-- Taxes will be loaded via AJAX -->
            </select>
        </td>
        <td>
            <div class="bg-light border rounded p-2 text-center">
                <span class="item-total">0.00</span>
            </div>
        </td>
        <td>
            <button type="button" class="btn btn-outline-danger btn-sm remove-item-btn">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    </tr>
</template>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let itemIndex = 0;

    // Customer type toggle
    document.querySelectorAll('input[name="customer_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const existingSection = document.getElementById('existing-customer-section');
            const newSection = document.getElementById('new-customer-section');

            if (this.value === 'existing') {
                existingSection.style.display = 'block';
                newSection.style.display = 'none';
                document.getElementById('customer_id').required = true;
                document.getElementById('new_customer_name').required = false;
                document.getElementById('new_customer_email').required = false;
            } else {
                existingSection.style.display = 'none';
                newSection.style.display = 'block';
                document.getElementById('customer_id').required = false;
                document.getElementById('new_customer_name').required = true;
                document.getElementById('new_customer_email').required = true;
            }
        });
    });

    // Add item functionality
    document.getElementById('add-item-btn').addEventListener('click', function() {
        addItem();
    });

    function addItem() {
        const template = document.getElementById('item-template');
        const container = document.getElementById('items-container');
        const clone = template.content.cloneNode(true);

        // Replace INDEX with actual index
        clone.innerHTML = clone.innerHTML.replace(/INDEX/g, itemIndex);

        // Add event listeners for calculation
        const itemRow = clone.querySelector('.item-row');
        addItemEventListeners(itemRow);

        container.appendChild(clone);
        itemIndex++;

        // Load units and taxes for new item
        if (itemIndex === 1) {
            loadUnitsAndTaxes();
        } else {
            // Copy options from first item
            copySelectOptions(itemRow);
        }

        calculateTotal();
    }

    function addItemEventListeners(itemRow) {
        const quantityInput = itemRow.querySelector('.item-quantity');
        const priceInput = itemRow.querySelector('.item-price');
        const removeBtn = itemRow.querySelector('.remove-item-btn');

        quantityInput.addEventListener('input', () => calculateItemTotal(itemRow));
        priceInput.addEventListener('input', () => calculateItemTotal(itemRow));

        removeBtn.addEventListener('click', function() {
            if (document.querySelectorAll('.item-row').length > 1) {
                itemRow.remove();
                calculateTotal();
            }
        });
    }

    function calculateItemTotal(itemRow) {
        const quantity = parseFloat(itemRow.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(itemRow.querySelector('.item-price').value) || 0;
        const total = quantity * price;

        itemRow.querySelector('.item-total').textContent = total.toFixed(2);
        calculateTotal();
    }

    function calculateTotal() {
        let total = 0;
        document.querySelectorAll('.item-total').forEach(element => {
            total += parseFloat(element.textContent) || 0;
        });

        document.getElementById('total-amount').textContent = total.toFixed(2);
    }

    function copySelectOptions(itemRow) {
        const firstUnitSelect = document.querySelector('.item-unit');
        const firstTaxSelect = document.querySelector('.item-tax');

        if (firstUnitSelect) {
            const newUnitSelect = itemRow.querySelector('.item-unit');
            newUnitSelect.innerHTML = firstUnitSelect.innerHTML;
        }

        if (firstTaxSelect) {
            const newTaxSelect = itemRow.querySelector('.item-tax');
            newTaxSelect.innerHTML = firstTaxSelect.innerHTML;
        }
    }

    function loadUnitsAndTaxes() {
        // Load units
        fetch('/api/bexio/units')
            .then(response => response.json())
            .then(data => {
                const unitSelects = document.querySelectorAll('.item-unit');
                unitSelects.forEach(select => {
                    select.innerHTML = '<option value="">Einheit wählen...</option>';
                    if (data && Array.isArray(data)) {
                        data.forEach(unit => {
                            select.innerHTML += `<option value="${unit.id}">${unit.name}</option>`;
                        });
                    }
                });
            })
            .catch(error => {
                console.error('Error loading units:', error);
                // Add default units
                const unitSelects = document.querySelectorAll('.item-unit');
                unitSelects.forEach(select => {
                    select.innerHTML = `
                        <option value="">Einheit wählen...</option>
                        <option value="1">Stück</option>
                        <option value="2">Stunden</option>
                        <option value="3">Tage</option>
                    `;
                });
            });

        // Load taxes
        fetch('/api/bexio/taxes')
            .then(response => response.json())
            .then(data => {
                const taxSelects = document.querySelectorAll('.item-tax');
                taxSelects.forEach(select => {
                    select.innerHTML = '<option value="">Steuer wählen...</option>';
                    if (data && Array.isArray(data)) {
                        data.forEach(tax => {
                            select.innerHTML += `<option value="${tax.id}">${tax.display_name}</option>`;
                        });
                    }
                });
            })
            .catch(error => {
                console.error('Error loading taxes:', error);
                // Add default taxes
                const taxSelects = document.querySelectorAll('.item-tax');
                taxSelects.forEach(select => {
                    select.innerHTML = `
                        <option value="">Steuer wählen...</option>
                        <option value="1">7.7% MwSt</option>
                        <option value="2">2.5% MwSt</option>
                        <option value="3">0% MwSt</option>
                    `;
                });
            });
    }

    // Load customers
    fetch('/api/bexio/customers')
        .then(response => response.json())
        .then(data => {
            const customerSelect = document.getElementById('customer_id');
            if (data && Array.isArray(data)) {
                data.forEach(customer => {
                    customerSelect.innerHTML += `<option value="${customer.id}">${customer.name_1 || customer.name}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('Error loading customers:', error);
        });

    // Create customer functionality
    document.getElementById('create-customer-btn').addEventListener('click', function() {
        const name = document.getElementById('new_customer_name').value;
        const email = document.getElementById('new_customer_email').value;
        const type = document.getElementById('new_customer_type').value;
        const address = document.getElementById('new_customer_address').value;
        const city = document.getElementById('new_customer_city').value;
        const postcode = document.getElementById('new_customer_postcode').value;

        if (!name || !email) {
            alert('Name und E-Mail sind erforderlich');
            return;
        }

        const customerData = {
            name: name,
            email: email,
            type: parseInt(type),
            address: address,
            city: city,
            postcode: postcode
        };

        fetch('/api/bexio/create-customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ customer: customerData })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Fehler beim Erstellen des Kunden: ' + data.message);
            } else {
                // Add new customer to select
                const customerSelect = document.getElementById('customer_id');
                customerSelect.innerHTML += `<option value="${data.id}" selected>${data.name}</option>`;

                // Switch to existing customer tab
                document.getElementById('existing_customer').checked = true;
                document.getElementById('existing_customer').dispatchEvent(new Event('change'));

                alert('Kunde erfolgreich erstellt!');
            }
        })
        .catch(error => {
            console.error('Error creating customer:', error);
            alert('Fehler beim Erstellen des Kunden');
        });
    });

    // Add first item by default
    addItem();
});
</script>
@endpush
