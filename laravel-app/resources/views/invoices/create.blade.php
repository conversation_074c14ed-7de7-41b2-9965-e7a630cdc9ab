@extends('layouts.app')

@section('title', 'Create New Invoice')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file-invoice me-2"></i>
                        <h5 class="mb-0 fw-semibold">Create New Invoice</h5>
                    </div>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('invoices.store') }}">
                        @csrf

                        <!-- Invoice Details Section -->
                        <div class="mb-4">
                            <h6 class="fw-semibold mb-3">Invoice Details</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="document_nr" class="form-label">Document Number</label>
                                    <input type="text" class="form-control" id="document_nr" name="document_nr" 
                                           value="{{ old('document_nr', 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT)) }}" required>
                                    <div class="form-text">Unique identifier for this invoice</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="total" class="form-label">Total Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">CHF</span>
                                        <input type="number" class="form-control" id="total" name="total" 
                                               value="{{ old('total') }}" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information Section -->
                        <div class="mb-4">
                            <h6 class="fw-semibold mb-3">Customer Information</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="contact_name" class="form-label">Customer Name</label>
                                    <input type="text" class="form-control" id="contact_name" name="contact_name" 
                                           value="{{ old('contact_name') }}" required>
                                    <div class="form-text">Company or individual name</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="contact_email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           value="{{ old('contact_email') }}" required>
                                </div>
                                <div class="col-12">
                                    <label for="contact_address" class="form-label">Address</label>
                                    <textarea class="form-control" id="contact_address" name="contact_address" rows="3">{{ old('contact_address') }}</textarea>
                                    <div class="form-text">Full address including city and postal code</div>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Status Section -->
                        <div class="mb-4">
                            <h6 class="fw-semibold mb-3">Invoice Status</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="sent" {{ old('status') == 'sent' ? 'selected' : '' }}>Sent</option>
                                        <option value="paid" {{ old('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                                        <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Recurring Settings Section -->
                        <div class="mb-4">
                            <h6 class="fw-semibold mb-3">Recurring Settings</h6>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" 
                                       value="1" {{ old('is_recurring') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_recurring">
                                    Make this a recurring invoice
                                </label>
                            </div>

                            <div id="recurring_options" style="display: {{ old('is_recurring') ? 'block' : 'none' }};">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="recurring_interval" class="form-label">Billing Interval</label>
                                        <select class="form-select" id="recurring_interval" name="recurring_interval">
                                            <option value="">Select interval</option>
                                            <option value="daily" {{ old('recurring_interval') == 'daily' ? 'selected' : '' }}>Daily</option>
                                            <option value="weekly" {{ old('recurring_interval') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                            <option value="monthly" {{ old('recurring_interval') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="next_run_date" class="form-label">Next Billing Date</label>
                                        <input type="date" class="form-control" id="next_run_date" name="next_run_date" 
                                               value="{{ old('next_run_date') }}" min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Create Invoice
                            </button>
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('is_recurring').addEventListener('change', function() {
    const recurringOptions = document.getElementById('recurring_options');
    const recurringInterval = document.getElementById('recurring_interval');
    const nextRunDate = document.getElementById('next_run_date');
    
    if (this.checked) {
        recurringOptions.style.display = 'block';
        recurringInterval.required = true;
        nextRunDate.required = true;
    } else {
        recurringOptions.style.display = 'none';
        recurringInterval.required = false;
        nextRunDate.required = false;
        recurringInterval.value = '';
        nextRunDate.value = '';
    }
});
</script>
@endsection
