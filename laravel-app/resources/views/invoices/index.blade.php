@extends('layouts.app')

@section('title', 'All Invoices')

@section('content')
<div class="container">
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Invoice Management</h6>
                    <h1 class="display-5 fw-bold mb-0">All Invoices</h1>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create New Invoice
                    </a>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-semibold">Your Invoices ({{ $invoices->total() }})</h5>
                </div>
                <div class="card-body p-0">
                    @if($invoices->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Document #</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Type</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoices as $invoice)
                                        <tr>
                                            <td>
                                                <strong>{{ $invoice->document_nr }}</strong>
                                                <br>
                                                <small class="text-muted">#{{ $invoice->bexio_id }}</small>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $invoice->contact_info['name'] ?? 'N/A' }}</strong>
                                                    @if(isset($invoice->contact_info['email']))
                                                        <br>
                                                        <small class="text-muted">{{ $invoice->contact_info['email'] }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <strong>CHF {{ number_format($invoice->total, 2) }}</strong>
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'draft' => 'secondary',
                                                        'sent' => 'primary',
                                                        'paid' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusColors[$invoice->status] ?? 'secondary' }}">
                                                    {{ ucfirst($invoice->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($invoice->is_recurring)
                                                    <span class="badge bg-info bg-opacity-10 text-info">
                                                        <i class="fas fa-sync-alt me-1"></i>Recurring
                                                    </span>
                                                    @if($invoice->recurringTemplate)
                                                        <br>
                                                        <small class="text-muted">{{ ucfirst($invoice->recurringTemplate->interval) }}</small>
                                                    @endif
                                                @else
                                                    <span class="badge bg-light text-dark">One-time</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $invoice->created_at->format('M j, Y') }}</small>
                                                <br>
                                                <small class="text-muted">{{ $invoice->created_at->format('g:i A') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-outline-secondary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('invoices.destroy', $invoice) }}" class="d-inline" 
                                                          onsubmit="return confirm('Are you sure you want to delete this invoice?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-outline-danger btn-sm">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($invoices->hasPages())
                            <div class="card-footer bg-light">
                                {{ $invoices->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No invoices found</h5>
                            <p class="text-muted">Create your first invoice to get started.</p>
                            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Create New Invoice
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
