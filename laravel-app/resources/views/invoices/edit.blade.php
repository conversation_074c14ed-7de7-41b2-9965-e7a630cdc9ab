@extends('layouts.rebill')

@php
    $title = "Edit Invoice";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-edit me-2"></i>
                <h1 class="mb-0">Edit Invoice #{{ $invoice->document_nr }}</h1>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-eye me-1"></i> View
                </a>
                <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to List
                </a>
            </div>
        </div>

        <form id="invoice-form" action="{{ route('invoices.update', $invoice) }}" method="POST">
            @csrf
            @method('PUT')

            <!-- Invoice Details Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Invoice Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rebill_title" class="form-label">Title in reBill <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="rebill_title" name="rebill_title" 
                                       value="{{ old('rebill_title', $invoice->rebill_title) }}" 
                                       placeholder="e.g. Construction Services..." required>
                                <div class="form-text">A short title to describe your invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="rebill_description" class="form-label">Description in reBill</label>
                                <textarea class="form-control" id="rebill_description" name="rebill_description" rows="3"
                                          placeholder="e.g. Ongoing development services...">{{ old('rebill_description', $invoice->rebill_description) }}</textarea>
                                <div class="form-text">Describe the products/services</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_nr" class="form-label">Document Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="document_nr" name="document_nr" 
                                       value="{{ old('document_nr', $invoice->document_nr) }}"
                                       placeholder="e.g. INV-2025-001" required>
                                <div class="form-text">Unique document number for this invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="bexio_title" class="form-label">Title in Bexio</label>
                                <input type="text" class="form-control" id="bexio_title" name="bexio_title" 
                                       value="{{ old('bexio_title', $invoice->bexio_title) }}"
                                       placeholder="e.g. Construction Services...">
                                <div class="form-text">Title for the Bexio invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="bexio_reference" class="form-label">Reference in Bexio</label>
                                <textarea class="form-control" id="bexio_reference" name="bexio_reference" rows="3"
                                          placeholder="e.g. Ongoing development services...">{{ old('bexio_reference', $invoice->bexio_reference) }}</textarea>
                                <div class="form-text">Reference for the Bexio invoice</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_name" name="contact_name" 
                                       value="{{ old('contact_name', $invoice->contact_name) }}" required readonly>
                                <div class="form-text">Customer name from Bexio</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Customer Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                       value="{{ old('contact_email', $invoice->contact_email) }}" required readonly>
                                <div class="form-text">Customer email from Bexio</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Billing Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="billing_period" class="form-label">Billing Period <span class="text-danger">*</span></label>
                                <select class="form-select" id="billing_period" name="billing_period" required>
                                    <option value="monthly" {{ old('billing_period', $invoice->billing_period) == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    <option value="yearly" {{ old('billing_period', $invoice->billing_period) == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                </select>
                                <div class="form-text">Your customer will be billed in this cycle</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ old('start_date', $invoice->start_date ? $invoice->start_date->format('Y-m-d') : '') }}" required>
                                <div class="form-text">Invoices will be sent to your customer starting from this date</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Status and Total -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Invoice Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="draft" {{ old('status', $invoice->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="sent" {{ old('status', $invoice->status) == 'sent' ? 'selected' : '' }}>Sent</option>
                                    <option value="paid" {{ old('status', $invoice->status) == 'paid' ? 'selected' : '' }}>Paid</option>
                                    <option value="cancelled" {{ old('status', $invoice->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="total" class="form-label">Total Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="total" name="total" 
                                           value="{{ old('total', $invoice->total) }}" step="0.01" min="0" required>
                                    <span class="input-group-text">CHF</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Invoice
                        </button>
                        <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@if ($errors->any())
    <div class="alert alert-danger mt-3">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

@endsection
