@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Ihr Abo ist inaktiv
                    </h4>
                </div>
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-4x text-warning mb-3"></i>
                        <h2 class="text-warning">Zugriff gesperrt</h2>
                        <p class="lead text-muted">
                            Ihr Abonnement für <strong>{{ $organization->name }}</strong> ist derzeit inaktiv.
                        </p>
                    </div>

                    <div class="alert alert-info">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-1"></i>
                            Was bedeutet das?
                        </h5>
                        <p class="mb-0">
                            Um Kim Rebill weiterhin nutzen zu kö<PERSON>n, benötigen Sie ein aktives Abonnement. 
                            Klicken Si<PERSON> unten, um Ihr Abonnement zu aktivieren.
                        </p>
                    </div>

                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-star me-1"></i>
                                        Kim Rebill Pro
                                    </h5>
                                    <div class="mb-3">
                                        <span class="h4 text-primary">CHF 29</span>
                                        <span class="text-muted">/Monat</span>
                                    </div>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-check text-success me-2"></i>Unbegrenzte Rechnungen</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Wiederkehrende Rechnungen</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Bexio Integration</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Email Support</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <form method="post" action="{{ route('subscription.request-activation') }}">
                            @csrf
                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-credit-card me-2"></i>
                                Zugriff aktivieren (kostenpflichtig)
                            </button>
                        </form>
                        
                        <p class="text-muted mt-3 small">
                            Nach dem Klick erhalten Sie eine E-Mail mit den Zahlungsdetails.
                        </p>
                    </div>

                    <hr class="my-4">

                    <div class="text-muted">
                        <p class="mb-2">
                            <strong>Fragen?</strong> Kontaktieren Sie uns:
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-envelope me-1"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
