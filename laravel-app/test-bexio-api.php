<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "🔧 Testing Bexio API with Real Access Token\n";
echo "==========================================\n\n";

// Get user with valid access token
$user = User::whereNotNull('access_token')->first();

if (!$user) {
    echo "❌ No user with access token found. Please login first.\n";
    exit(1);
}

echo "👤 User: {$user->name} ({$user->email})\n";
echo "🕐 Token expires: {$user->token_expires_at}\n";

// Check if token is still valid
$now = now();
$expiresAt = $user->token_expires_at;
$secondsUntilExpiry = $expiresAt->getTimestamp() - $now->getTimestamp();

echo "⏰ Seconds until expiry: {$secondsUntilExpiry}\n";

if ($secondsUntilExpiry <= 0) {
    echo "❌ Token has expired. Please login again.\n";
    exit(1);
}

echo "✅ Token is valid\n\n";

// Test API endpoints
$endpoints = [
    '2.0/contact' => 'Contacts (v2.0)',
    '3.0/contact' => 'Contacts (v3.0)', 
    '2.0/unit' => 'Units',
    '2.0/tax' => 'Taxes',
];

$headers = [
    'Accept' => 'application/json',
    'Authorization' => 'Bearer ' . $user->access_token,
    'Content-Type' => 'application/json'
];

$client = new \GuzzleHttp\Client();

foreach ($endpoints as $endpoint => $description) {
    echo "🔗 Testing {$description} ({$endpoint})\n";
    echo str_repeat("-", 50) . "\n";
    
    $url = 'https://api.bexio.com/' . $endpoint;
    
    try {
        $response = $client->request('GET', $url, [
            'headers' => $headers,
        ]);
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);
        
        echo "✅ Status: {$statusCode}\n";
        
        if (is_array($data)) {
            echo "📊 Records: " . count($data) . "\n";
            
            if (count($data) > 0) {
                echo "📋 Sample record:\n";
                $sample = $data[0];
                foreach ($sample as $key => $value) {
                    if (is_string($value) || is_numeric($value)) {
                        $displayValue = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                        echo "   {$key}: {$displayValue}\n";
                    }
                }
            }
        } else {
            echo "📄 Response: " . substr($body, 0, 200) . "...\n";
        }
        
    } catch (\GuzzleHttp\Exception\BadResponseException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
        $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';
        
        echo "❌ Error: {$statusCode}\n";
        echo "📄 Message: {$e->getMessage()}\n";
        echo "📄 Response: {$responseBody}\n";
        
    } catch (\Exception $e) {
        echo "❌ Exception: {$e->getMessage()}\n";
    }
    
    echo "\n";
}

// Test specific contact filtering
echo "🔍 Testing Contact Filtering\n";
echo str_repeat("-", 50) . "\n";

try {
    $response = $client->request('GET', 'https://api.bexio.com/2.0/contact', [
        'headers' => $headers,
    ]);
    
    $data = json_decode($response->getBody()->getContents(), true);
    
    if (is_array($data)) {
        $customers = array_filter($data, function($contact) {
            return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 1;
        });
        
        $suppliers = array_filter($data, function($contact) {
            return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 2;
        });
        
        echo "📊 Total contacts: " . count($data) . "\n";
        echo "👥 Customers (type 1): " . count($customers) . "\n";
        echo "🏢 Suppliers (type 2): " . count($suppliers) . "\n";
        
        if (count($customers) > 0) {
            echo "\n📋 Sample customers:\n";
            $sampleCustomers = array_slice($customers, 0, 3);
            foreach ($sampleCustomers as $customer) {
                $name = $customer['name_1'] ?? 'Unknown';
                $email = $customer['mail'] ?? 'No email';
                echo "   - {$name} ({$email})\n";
            }
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Error filtering contacts: {$e->getMessage()}\n";
}

echo "\n✨ Test completed!\n";
