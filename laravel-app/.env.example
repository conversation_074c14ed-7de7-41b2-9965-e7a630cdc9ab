APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Bexio OAuth Configuration
BEXIO_CLIENT_ID=your-bexio-client-id
BEXIO_CLIENT_SECRET=your-bexio-client-secret
BEXIO_REDIRECT_URI="http://127.0.0.1:8000/auth/callback"

# Bexio API Endpoints
BEXIO_AUTH_BASE_URL="https://auth.bexio.com/realms/bexio"
BEXIO_API_BASE_URL="https://api.bexio.com"
BEXIO_AUTH_ENDPOINT="https://auth.bexio.com/realms/bexio/protocol/openid-connect/auth"
BEXIO_TOKEN_ENDPOINT="https://auth.bexio.com/realms/bexio/protocol/openid-connect/token"
BEXIO_USERINFO_ENDPOINT="https://auth.bexio.com/realms/bexio/protocol/openid-connect/userinfo"

# Bexio OAuth Scopes (comma-separated)
BEXIO_SCOPES="openid,profile,email,offline_access,kb_invoice_edit,contact_edit,company_profile"

# Bexio Legacy URLs (for backward compatibility)
BEXIO_AUTH_URL="https://auth.bexio.com/oauth/authorize"
BEXIO_TOKEN_URL="https://auth.bexio.com/oauth/access_token"

# Bexio Token Configuration
BEXIO_TOKEN_EXPIRES_IN=3600
BEXIO_TOKEN_REFRESH_THRESHOLD=600
