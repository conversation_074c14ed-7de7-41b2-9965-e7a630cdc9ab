<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Http\Controllers\Api\BexioApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

echo "🔧 Testing Personal Access Token\n";
echo "===============================\n\n";

// Check configuration
echo "🔑 Personal Token configured: " . (config('bexio.personal_access_token') ? 'YES' : 'NO') . "\n";
echo "🔄 Use Personal Token: " . (config('bexio.use_personal_token') ? 'YES' : 'NO') . "\n";

if (config('bexio.personal_access_token')) {
    $token = config('bexio.personal_access_token');
    echo "📄 Token preview: " . substr($token, 0, 50) . "...\n";
}

echo "\n";

// Test API Controller methods
$controller = new BexioApiController();

// Test each method
$methods = [
    'getCustomers' => 'Customers',
    'getUnits' => 'Units', 
    'getTaxes' => 'Taxes'
];

foreach ($methods as $method => $description) {
    echo "🔗 Testing {$description} ({$method})\n";
    echo str_repeat("-", 40) . "\n";
    
    try {
        $response = $controller->$method();
        $data = json_decode($response->getContent(), true);
        
        if (isset($data['error'])) {
            echo "❌ API Error: {$data['error']}\n";
            if (isset($data['message'])) {
                echo "📄 Message: {$data['message']}\n";
            }
        } else if (is_array($data)) {
            echo "✅ Success: " . count($data) . " records\n";
            
            if (count($data) > 0) {
                echo "📋 Sample record:\n";
                $sample = $data[0];
                $displayCount = 0;
                foreach ($sample as $key => $value) {
                    if ($displayCount >= 5) break; // Limit display
                    if (is_string($value) || is_numeric($value)) {
                        $displayValue = strlen($value) > 30 ? substr($value, 0, 30) . '...' : $value;
                        echo "   {$key}: {$displayValue}\n";
                        $displayCount++;
                    }
                }
                
                // Special handling for customers
                if ($method === 'getCustomers' && isset($sample['contact_type_id'])) {
                    $customers = array_filter($data, function($contact) {
                        return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 1;
                    });
                    echo "   👥 Customers (type 1): " . count($customers) . "\n";
                }
            }
        } else {
            echo "⚠️  Unexpected response format\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Exception: {$e->getMessage()}\n";
    }
    
    echo "\n";
}

echo "💡 Next Steps:\n";
echo "=============\n";
echo "1. If successful, test create invoice page\n";
echo "2. Customer dropdown should now load real data\n";
echo "3. Units and taxes should also work\n";

echo "\n✨ Test completed!\n";
