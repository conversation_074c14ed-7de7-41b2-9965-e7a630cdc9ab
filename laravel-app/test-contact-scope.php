<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "🔧 Testing Contact API with contact_show Scope\n";
echo "==============================================\n\n";

// Check current scope configuration
$configuredScopes = config('bexio.scopes');
echo "🔑 Configured scopes: " . implode(', ', $configuredScopes) . "\n";

if (in_array('contact_show', $configuredScopes)) {
    echo "✅ contact_show scope is configured\n";
} else {
    echo "❌ contact_show scope is missing\n";
    echo "💡 Add 'contact_show' to BEXIO_SCOPES in .env\n";
    exit(1);
}

// Get user with valid access token
$user = User::whereNotNull('access_token')->first();

if (!$user) {
    echo "\n❌ No user with access token found.\n";
    echo "📋 Steps to test:\n";
    echo "1. Login again at: http://127.0.0.1:8000/login\n";
    echo "2. This will request new token with contact_show scope\n";
    echo "3. Run this test again\n";
    exit(1);
}

echo "\n👤 User: {$user->name}\n";
echo "🕐 Token expires: {$user->token_expires_at}\n";

// Check if token is still valid
$now = now();
$expiresAt = $user->token_expires_at;
$secondsUntilExpiry = $expiresAt->getTimestamp() - $now->getTimestamp();

echo "⏰ Seconds until expiry: {$secondsUntilExpiry}\n";

if ($secondsUntilExpiry <= 0) {
    echo "❌ Token has expired. Please login again.\n";
    exit(1);
}

echo "✅ Token is valid\n\n";

// Test contact endpoint
echo "🔗 Testing Contact Endpoint\n";
echo "===========================\n";

$headers = [
    'Accept' => 'application/json',
    'Authorization' => 'Bearer ' . $user->access_token,
    'Content-Type' => 'application/json'
];

$client = new \GuzzleHttp\Client();

$endpoints = [
    '2.0/contact' => 'Contacts v2.0',
    '3.0/contact' => 'Contacts v3.0'
];

foreach ($endpoints as $endpoint => $description) {
    echo "📞 {$description}: ";
    
    try {
        $response = $client->request('GET', 'https://api.bexio.com/' . $endpoint, [
            'headers' => $headers,
            'timeout' => 10
        ]);
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);
        
        if ($statusCode == 200 && is_array($data)) {
            echo "✅ {$statusCode} - " . count($data) . " contacts\n";
            
            // Filter customers
            $customers = array_filter($data, function($contact) {
                return isset($contact['contact_type_id']) && $contact['contact_type_id'] == 1;
            });
            
            echo "   👥 Customers: " . count($customers) . "\n";
            
            if (count($customers) > 0) {
                echo "   📋 Sample customers:\n";
                $sampleCustomers = array_slice($customers, 0, 3);
                foreach ($sampleCustomers as $customer) {
                    $name = $customer['name_1'] ?? 'Unknown';
                    $email = $customer['mail'] ?? 'No email';
                    echo "      - {$name} ({$email})\n";
                }
            }
        } else {
            echo "⚠️  {$statusCode}\n";
        }
        
    } catch (\GuzzleHttp\Exception\ClientException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
        $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : '';
        
        echo "❌ {$statusCode}";
        
        if ($statusCode == 403) {
            echo " (Forbidden)\n";
            echo "   💡 Token may not have contact_show scope\n";
            echo "   📋 Please login again to get new token\n";
        } elseif ($statusCode == 404) {
            echo " (Not Found)\n";
        } elseif ($statusCode == 401) {
            echo " (Unauthorized)\n";
        } else {
            echo "\n";
        }
        
        if ($responseBody) {
            $errorData = json_decode($responseBody, true);
            if ($errorData && isset($errorData['message'])) {
                echo "   📄 Error: {$errorData['message']}\n";
            }
        }
        
    } catch (\Exception $e) {
        echo "❌ Exception: " . substr($e->getMessage(), 0, 50) . "...\n";
    }
    
    echo "\n";
}

echo "💡 Next Steps:\n";
echo "=============\n";
echo "1. If still getting 403, login again to get token with contact_show scope\n";
echo "2. If successful, update BexioApiController to use working endpoint\n";
echo "3. Test create invoice page customer dropdown\n";

echo "\n✨ Test completed!\n";
