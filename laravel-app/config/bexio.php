<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Bexio Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Bexio OAuth integration
    |
    */

    'client_id' => env('BEXIO_CLIENT_ID'),

    'client_secret' => env('BEXIO_CLIENT_SECRET'),

    'redirect_uri' => env('BEXIO_REDIRECT_URI', 'http://localhost/auth/callback'),

    'scopes' => [
        'openid',
        'profile',
        'email',
        'offline_access',
        'kb_invoice_edit',
        'contact_edit',
        'company_profile',
    ],

    'auth_url' => 'https://auth.bexio.com/oauth/authorize',
    'token_url' => 'https://auth.bexio.com/oauth/access_token',
    'api_base_url' => 'https://api.bexio.com',

    // OpenID Connect Configuration for token refresh
    'oidc_issuer' => env('BEXIO_OIDC_ISSUER', 'https://auth.bexio.com/realms/bexio'),
    'oidc_token_endpoint' => env('BEXIO_OIDC_TOKEN_ENDPOINT', 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/token'),
    'token_expires_in' => env('BEXIO_TOKEN_EXPIRES_IN', 3600), // seconds (1 hour)
    'token_refresh_threshold' => env('BEXIO_TOKEN_REFRESH_THRESHOLD', 300), // seconds (5 minutes before expiry)
];
