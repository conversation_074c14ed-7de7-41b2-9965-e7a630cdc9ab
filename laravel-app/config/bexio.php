<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Bexio Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Bexio OAuth integration
    |
    */

    'client_id' => env('BEXIO_CLIENT_ID'),

    'client_secret' => env('BEXIO_CLIENT_SECRET'),

    'redirect_uri' => env('BEXIO_REDIRECT_URI', 'http://localhost/auth/callback'),

    // OAuth Scopes - configurable via environment
    'scopes' => explode(',', env('BEXIO_SCOPES', 'openid,profile,email,offline_access,kb_invoice_edit,contact_edit,company_profile')),

    // Legacy OAuth URLs (for backward compatibility)
    'auth_url' => env('BEXIO_AUTH_URL', 'https://auth.bexio.com/oauth/authorize'),
    'token_url' => env('BEXIO_TOKEN_URL', 'https://auth.bexio.com/oauth/access_token'),

    // Bexio API Endpoints (OpenID Connect)
    'auth_base_url' => env('BEXIO_AUTH_BASE_URL', 'https://auth.bexio.com/realms/bexio'),
    'api_base_url' => env('BEXIO_API_BASE_URL', 'https://api.bexio.com'),
    'auth_endpoint' => env('BEXIO_AUTH_ENDPOINT', 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/auth'),
    'token_endpoint' => env('BEXIO_TOKEN_ENDPOINT', 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/token'),
    'userinfo_endpoint' => env('BEXIO_USERINFO_ENDPOINT', 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/userinfo'),

    // Token Configuration
    'token_expires_in' => env('BEXIO_TOKEN_EXPIRES_IN', 3600), // seconds (1 hour)
    'token_refresh_threshold' => env('BEXIO_TOKEN_REFRESH_THRESHOLD', 600), // seconds (10 minutes before expiry)

    // Personal Access Token (alternative to OAuth)
    'personal_access_token' => env('BEXIO_PERSONAL_ACCESS_TOKEN'),
    'use_personal_token' => env('BEXIO_USE_PERSONAL_TOKEN', false),
];
