<?php

/**
 * Test OAuth URL Generation with Correct Scopes
 */

echo "🔗 Testing OAuth URL Generation\n";
echo "==============================\n\n";

// Load .env file manually
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env = [];
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
            [$key, $value] = explode('=', $line, 2);
            $env[trim($key)] = trim($value, '"');
        }
    }
    
    $clientId = $env['BEXIO_CLIENT_ID'] ?? '';
    $redirectUri = $env['BEXIO_REDIRECT_URI'] ?? '';
    $authEndpoint = $env['BEXIO_AUTH_ENDPOINT'] ?? '';
    
    // Scopes matching PHP original
    $scopes = [
        'openid',
        'profile', 
        'email',
        'company_profile',
        'offline_access',
        'kb_invoice_edit',
        'contact_edit'
    ];
    
    echo "📋 Configuration:\n";
    echo "Client ID: {$clientId}\n";
    echo "Redirect URI: {$redirectUri}\n";
    echo "Auth Endpoint: {$authEndpoint}\n";
    echo "Scopes: " . implode(' ', $scopes) . "\n\n";
    
    if ($authEndpoint && $clientId && $redirectUri) {
        $authUrl = $authEndpoint . '?' . http_build_query([
            'client_id' => $clientId,
            'redirect_uri' => $redirectUri,
            'response_type' => 'code',
            'scope' => implode(' ', $scopes),
            'state' => 'test_state_' . time()
        ]);
        
        echo "✅ OAuth URL generated successfully:\n";
        echo "🔗 {$authUrl}\n\n";
        
        // Test if scopes are correct
        if (strpos($authUrl, 'company_profile') !== false) {
            echo "✅ company_profile scope: included\n";
        } else {
            echo "❌ company_profile scope: missing\n";
        }
        
        if (strpos($authUrl, 'kb_invoice_edit') !== false) {
            echo "✅ kb_invoice_edit scope: included\n";
        } else {
            echo "❌ kb_invoice_edit scope: missing\n";
        }
        
        if (strpos($authUrl, 'contact_edit') !== false) {
            echo "✅ contact_edit scope: included\n";
        } else {
            echo "❌ contact_edit scope: missing\n";
        }
        
        if (strpos($authUrl, 'offline_access') !== false) {
            echo "✅ offline_access scope: included\n";
        } else {
            echo "❌ offline_access scope: missing\n";
        }
        
    } else {
        echo "❌ Cannot generate OAuth URL - missing configuration\n";
    }
} else {
    echo "❌ .env file not found\n";
}

echo "\n✨ Test completed!\n";
