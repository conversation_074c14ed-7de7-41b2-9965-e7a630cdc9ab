<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use App\Models\User;
use App\Models\Organization;
use Carbon\Carbon;

class BexioAuthMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('bexio.mode', 'demo');
        Config::set('bexio.token_refresh_threshold', 300); // 5 minutes
        Config::set('bexio.token_expires_in', 3600); // 1 hour
    }

    /** @test */
    public function it_allows_access_with_valid_token_and_active_subscription()
    {
        $organization = Organization::factory()->create([
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(30),
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'valid_token',
            'token_expires_at' => now()->addHours(2), // Token valid for 2 hours
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function it_redirects_unauthenticated_users_to_login()
    {
        $response = $this->get('/dashboard');
        
        $response->assertRedirect('/login');
    }

    /** @test */
    public function it_handles_users_without_organization()
    {
        $user = User::factory()->create([
            'organization_id' => null,
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/login');
        $response->assertSessionHasErrors(['auth']);
        $this->assertGuest();
    }

    /** @test */
    public function it_blocks_access_for_inactive_subscription()
    {
        $organization = Organization::factory()->create([
            'subscription_status' => 'inactive',
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'valid_token',
            'token_expires_at' => now()->addHours(2),
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(403);
        $response->assertViewIs('subscription.inactive');
    }

    /** @test */
    public function it_blocks_access_for_expired_trial()
    {
        $organization = Organization::factory()->create([
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->subDays(1), // Trial expired yesterday
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'valid_token',
            'token_expires_at' => now()->addHours(2),
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(403);
        $response->assertViewIs('subscription.trial-expired');
    }

    /** @test */
    public function it_allows_access_for_active_trial()
    {
        $organization = Organization::factory()->create([
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(15), // Trial active for 15 more days
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'valid_token',
            'token_expires_at' => now()->addHours(2),
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function it_allows_access_for_paid_subscription()
    {
        $organization = Organization::factory()->create([
            'subscription_status' => 'active',
            'trial_ends_at' => now()->subDays(30), // Trial ended, but has paid subscription
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'valid_token',
            'token_expires_at' => now()->addHours(2),
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function it_skips_middleware_for_auth_routes()
    {
        // Test that OAuth callback routes are not blocked by middleware
        $response = $this->get('/auth/callback?code=test');
        
        // Should not redirect to login, should process the callback
        $this->assertNotEquals('/login', $response->headers->get('Location'));
    }

    /** @test */
    public function it_handles_token_refresh_when_near_expiry()
    {
        Config::set('bexio.mode', 'demo'); // Use demo mode to avoid real API calls
        
        $organization = Organization::factory()->create([
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(30),
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'expiring_token',
            'refresh_token' => 'refresh_token',
            'token_expires_at' => now()->addMinutes(2), // Token expires in 2 minutes (< 5 min threshold)
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        // In demo mode, token refresh should work
        $response->assertStatus(200);
    }

    /** @test */
    public function it_logs_out_user_when_token_refresh_fails()
    {
        Config::set('bexio.mode', 'real'); // Use real mode to trigger actual refresh failure
        Config::set('bexio.client_id', null); // Missing credentials will cause refresh to fail
        
        $organization = Organization::factory()->create([
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(30),
        ]);
        
        $user = User::factory()->create([
            'organization_id' => $organization->id,
            'access_token' => 'expiring_token',
            'refresh_token' => 'invalid_refresh_token',
            'token_expires_at' => now()->addMinutes(2), // Token expires in 2 minutes
        ]);
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertRedirect('/login');
        $response->assertSessionHasErrors(['auth']);
        $this->assertGuest();
    }
}
