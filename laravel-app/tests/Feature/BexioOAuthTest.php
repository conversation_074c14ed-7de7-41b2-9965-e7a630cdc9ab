<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;
use App\Models\User;
use App\Models\Organization;

class BexioOAuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('bexio.mode', 'demo');
        Config::set('bexio.client_id', 'test_client_id');
        Config::set('bexio.client_secret', 'test_client_secret');
        Config::set('bexio.redirect_uri', 'http://127.0.0.1:8000/auth/callback');
    }

    /** @test */
    public function it_shows_login_form()
    {
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertSee('Login with Bexio');
    }

    /** @test */
    public function it_redirects_authenticated_users_from_login()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)->get('/login');
        
        $response->assertRedirect('/dashboard');
    }

    /** @test */
    public function it_redirects_to_bexio_oauth_in_demo_mode()
    {
        Config::set('bexio.mode', 'demo');
        
        $response = $this->get('/auth/bexio');
        
        // In demo mode, it should redirect to callback with mock code
        $response->assertRedirect();
        $this->assertStringContainsString('/auth/callback', $response->headers->get('Location'));
    }

    /** @test */
    public function it_handles_oauth_callback_in_demo_mode()
    {
        Config::set('bexio.mode', 'demo');
        
        $response = $this->get('/auth/callback?code=mock_code');
        
        $response->assertRedirect('/dashboard');
        
        // Check that user was created and logged in
        $this->assertAuthenticated();
        
        $user = auth()->user();
        $this->assertNotNull($user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertNotNull($user->organization);
        $this->assertEquals('Demo Organization', $user->organization->name);
    }

    /** @test */
    public function it_handles_oauth_callback_failure_gracefully()
    {
        Config::set('bexio.mode', 'demo');
        
        // Test callback without code parameter
        $response = $this->get('/auth/callback');
        
        $response->assertRedirect('/login');
        $response->assertSessionHasErrors(['bexio']);
    }

    /** @test */
    public function it_handles_personal_token_login_in_token_mode()
    {
        Config::set('bexio.mode', 'token');
        Config::set('bexio.personal_token', 'test_token');
        
        $response = $this->get('/auth/personal-token');
        
        // Should redirect to dashboard after successful token login
        $response->assertRedirect('/dashboard');
        
        // Check that user was created and logged in
        $this->assertAuthenticated();
        
        $user = auth()->user();
        $this->assertNotNull($user);
        $this->assertNotNull($user->organization);
    }

    /** @test */
    public function it_requires_guest_middleware_for_oauth_routes()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)->get('/auth/bexio');
        $response->assertRedirect('/dashboard');
        
        $response = $this->actingAs($user)->get('/login');
        $response->assertRedirect('/dashboard');
    }

    /** @test */
    public function it_handles_logout_correctly()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)->post('/logout');
        
        $response->assertRedirect('/');
        $this->assertGuest();
    }

    /** @test */
    public function it_redirects_unauthenticated_users_to_login()
    {
        $response = $this->get('/dashboard');
        
        $response->assertRedirect('/login');
    }

    /** @test */
    public function it_handles_missing_bexio_credentials_gracefully()
    {
        Config::set('bexio.client_id', null);
        Config::set('bexio.client_secret', null);
        Config::set('bexio.mode', 'real');
        
        $response = $this->get('/auth/bexio');
        
        $response->assertRedirect('/login');
        $response->assertSessionHasErrors(['auth']);
    }

    /** @test */
    public function it_creates_organization_with_correct_trial_status()
    {
        Config::set('bexio.mode', 'demo');
        
        $response = $this->get('/auth/callback?code=mock_code');
        
        $user = auth()->user();
        $organization = $user->organization;
        
        $this->assertEquals('trial', $organization->subscription_status);
        $this->assertNotNull($organization->trial_ends_at);
        $this->assertTrue($organization->isTrial());
        $this->assertFalse($organization->isTrialExpired());
    }

    /** @test */
    public function it_stores_bexio_tokens_correctly()
    {
        Config::set('bexio.mode', 'demo');
        
        $response = $this->get('/auth/callback?code=mock_code');
        
        $user = auth()->user();
        
        $this->assertNotNull($user->access_token);
        $this->assertNotNull($user->refresh_token);
        $this->assertNotNull($user->token_expires_at);
        $this->assertNotNull($user->bexio_user_id);
        $this->assertNotNull($user->bexio_company_id);
    }
}
