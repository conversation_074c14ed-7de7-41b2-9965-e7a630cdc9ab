<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use App\Models\User;
use App\Models\Organization;
use App\Helpers\BexioAuth;

class BexioAuthHelperTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        Config::set('bexio.token_refresh_threshold', 300); // 5 minutes
        Config::set('bexio.token_expires_in', 3600); // 1 hour
    }

    /** @test */
    public function it_returns_null_when_token_is_fresh()
    {
        $user = User::factory()->create([
            'access_token' => 'fresh_token',
            'token_expires_at' => now()->addHours(2), // Token valid for 2 hours
        ]);
        
        $this->actingAs($user);
        
        $result = BexioAuth::requireLoginAndFreshToken();
        
        $this->assertNull($result);
    }

    /** @test */
    public function it_returns_login_redirect_when_user_not_authenticated()
    {
        $result = BexioAuth::requireLoginAndFreshToken();
        
        $this->assertNotNull($result);
        $this->assertEquals('/login', $result->headers->get('Location'));
    }

    /** @test */
    public function it_attempts_token_refresh_when_token_near_expiry()
    {
        Config::set('bexio.mode', 'demo'); // Use demo mode to avoid real API calls
        
        $user = User::factory()->create([
            'access_token' => 'expiring_token',
            'refresh_token' => 'refresh_token',
            'token_expires_at' => now()->addMinutes(2), // Token expires in 2 minutes (< 5 min threshold)
        ]);
        
        $this->actingAs($user);
        
        $result = BexioAuth::requireLoginAndFreshToken();
        
        // In demo mode, this should return null (success)
        $this->assertNull($result);
    }

    /** @test */
    public function it_returns_access_token_for_authenticated_user()
    {
        $user = User::factory()->create([
            'access_token' => 'test_access_token',
        ]);
        
        $this->actingAs($user);
        
        $token = BexioAuth::getAccessToken();
        
        $this->assertEquals('test_access_token', $token);
    }

    /** @test */
    public function it_returns_null_access_token_for_unauthenticated_user()
    {
        $token = BexioAuth::getAccessToken();
        
        $this->assertNull($token);
    }

    /** @test */
    public function it_correctly_identifies_admin_users()
    {
        $adminUser = User::factory()->create([
            'is_admin' => true,
        ]);
        
        $regularUser = User::factory()->create([
            'is_admin' => false,
        ]);
        
        $this->actingAs($adminUser);
        $this->assertTrue(BexioAuth::isAdmin());
        
        $this->actingAs($regularUser);
        $this->assertFalse(BexioAuth::isAdmin());
    }

    /** @test */
    public function it_returns_false_for_admin_check_when_unauthenticated()
    {
        $this->assertFalse(BexioAuth::isAdmin());
    }

    /** @test */
    public function it_handles_missing_token_expiry_gracefully()
    {
        $user = User::factory()->create([
            'access_token' => 'token_without_expiry',
            'token_expires_at' => null,
        ]);
        
        $this->actingAs($user);
        
        $result = BexioAuth::requireLoginAndFreshToken();
        
        // Should not attempt refresh when no expiry is set
        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_token_refresh_failure_gracefully()
    {
        Config::set('bexio.mode', 'real'); // Use real mode
        Config::set('bexio.client_id', null); // Missing credentials will cause failure
        
        $user = User::factory()->create([
            'access_token' => 'expiring_token',
            'refresh_token' => 'invalid_refresh_token',
            'token_expires_at' => now()->addMinutes(2), // Token expires soon
        ]);
        
        $this->actingAs($user);
        
        $result = BexioAuth::requireLoginAndFreshToken();
        
        // Should redirect to login when refresh fails
        $this->assertNotNull($result);
        $this->assertEquals('/login', $result->headers->get('Location'));
        
        // User should be logged out
        $this->assertGuest();
    }
}
