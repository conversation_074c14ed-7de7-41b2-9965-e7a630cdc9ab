<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Http\Controllers\Api\BexioApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

echo "🔧 Testing BexioApiController Methods\n";
echo "====================================\n\n";

// Get user with valid access token
$user = User::whereNotNull('access_token')->first();

if (!$user) {
    echo "❌ No user with access token found. Please login first.\n";
    exit(1);
}

// Simulate authentication
Auth::login($user);

echo "👤 User: {$user->name}\n";
echo "✅ User authenticated\n\n";

$controller = new BexioApiController();

// Test each method
$methods = [
    'getCustomers' => 'Customers',
    'getUnits' => 'Units', 
    'getTaxes' => 'Taxes'
];

foreach ($methods as $method => $description) {
    echo "🔗 Testing {$description} ({$method})\n";
    echo str_repeat("-", 40) . "\n";
    
    try {
        $response = $controller->$method();
        $data = json_decode($response->getContent(), true);
        
        if (isset($data['error'])) {
            echo "❌ API Error: {$data['error']}\n";
            if (isset($data['message'])) {
                echo "📄 Message: {$data['message']}\n";
            }
        } else if (is_array($data)) {
            echo "✅ Success: " . count($data) . " records\n";
            
            if (count($data) > 0) {
                echo "📋 Sample record:\n";
                $sample = $data[0];
                foreach ($sample as $key => $value) {
                    if (is_string($value) || is_numeric($value)) {
                        $displayValue = strlen($value) > 30 ? substr($value, 0, 30) . '...' : $value;
                        echo "   {$key}: {$displayValue}\n";
                    }
                }
            }
        } else {
            echo "⚠️  Unexpected response format\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Exception: {$e->getMessage()}\n";
    }
    
    echo "\n";
}

echo "✨ Test completed!\n";
