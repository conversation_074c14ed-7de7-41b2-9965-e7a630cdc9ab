<?php
session_start();
require_once __DIR__ . '/../db/db.php';

function requireLoginAndFreshToken() {
    if (!isset($_SESSION['user_id'])) {
        header("Location: /login.php");
        exit;
    }

    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();

    if (!$user) {
        header("Location: /login.php");
        exit;
    }

    $stmt = $pdo->prepare("SELECT subscription_status FROM organizations WHERE id = ?");
    $stmt->execute([$user['organization_id']]);
    $org = $stmt->fetch();

    if (!$org || ($org['subscription_status'] === 'inactive')) {
        echo "<h2>Ihr Abo ist inaktiv.</h2>";
        echo "<form method='post' action='/request_activation.php'><button type='submit'>Zugriff aktivieren (kostenpflichtig)</button></form>";
        exit;
    }

    if (strtotime($user['token_expires_at']) - time() < 600) {
        $client_id = 'fb7bcebd-1402-4791-b27d-e5f742f01490';
        $client_secret = 'AO8Wr1q37jA82R8DMpcpejj_8x49t81RBW5lWGGKawvIBxkZ7eBC7Ixx4LyiS92D7VGdDiA57ASqowoF2AcKnJ4';

        $ch = curl_init('https://auth.bexio.com/oauth/access_token');
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query([
                'grant_type' => 'refresh_token',
                'refresh_token' => $user['refresh_token'],
                'client_id' => $client_id,
                'client_secret' => $client_secret,
            ]),
        ]);

        $response = curl_exec($ch);
        $data = json_decode($response, true);

        if (isset($data['access_token'])) {
            $access_token = $data['access_token'];
            $refresh_token = $data['refresh_token'] ?? $user['refresh_token'];
            $expires_in = $data['expires_in'];

            $stmt = $pdo->prepare("UPDATE users SET access_token = ?, refresh_token = ?, token_expires_at = DATE_ADD(NOW(), INTERVAL ? SECOND) WHERE id = ?");
            $stmt->execute([$access_token, $refresh_token, $expires_in, $_SESSION['user_id']]);

            $user['access_token'] = $access_token;
            $user['refresh_token'] = $refresh_token;
            $user['token_expires_at'] = date('Y-m-d H:i:s', time() + $expires_in);
        } else {
            session_destroy();
            header("Location: /login.php");
            exit;
        }
    }

    return $user;
}
?>