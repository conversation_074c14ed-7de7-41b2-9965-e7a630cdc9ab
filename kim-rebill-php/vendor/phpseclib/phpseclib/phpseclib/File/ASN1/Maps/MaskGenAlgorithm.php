<?php

/**
 * MaskGenAglorithm
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

namespace phpseclib3\File\ASN1\Maps;

/**
 * MaskGenAglorithm
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class MaskGenAlgorithm
{
    const MAP = AlgorithmIdentifier::MAP;
}
