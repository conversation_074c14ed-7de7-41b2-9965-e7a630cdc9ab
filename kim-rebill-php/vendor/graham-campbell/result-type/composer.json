{"name": "graham-campbell/result-type", "description": "An Implementation Of The Result Type", "keywords": ["result", "result-type", "Result", "Result Type", "Result-Type", "<PERSON>", "Graham<PERSON><PERSON><PERSON>"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "autoload-dev": {"psr-4": {"GrahamCampbell\\Tests\\ResultType\\": "tests/"}}, "config": {"preferred-install": "dist"}}