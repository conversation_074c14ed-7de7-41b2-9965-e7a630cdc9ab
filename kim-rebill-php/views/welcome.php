<?php
session_start();
$title = "Willkommen bei reBill";

// Redirect falls bereits eingeloggt
if (isset($_SESSION['organization_id'])) {
    header("Location: /dashboard");
    exit;
}

$alert = null;
if (isset($_SESSION['auth_error'])) {
    $alert = $_SESSION['auth_error'];
    unset($_SESSION['auth_error']);
}

ob_start();
?>

<div class="row justify-content-center">
    <div class="col-md-6 text-center">
        <h1 class="mb-4">Willkommen bei <strong>reBill</strong></h1>

        <p>
            <a href="/login" class="btn btn-dark btn-lg align-middle" style="background-color: #0D2F3B !important; font-weight: 600;">Login mit <img src="/assets/bexio.png" alt="bexio" style="max-width: 80px;border-radius: 5px; margin-right: -8px; margin-top: -10px;" /></a>
        </p>
    </div>
</div>

<?php
$content = ob_get_clean();
require __DIR__ . '/layout.php';
?>
