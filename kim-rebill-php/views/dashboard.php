<?php
require_once __DIR__ . '/../db/db.php';
session_start();
$org_id = $_SESSION['organization_id'] ?? null;
if (!$org_id) { header("Location: /login"); exit; }
$stmt = $pdo->prepare("SELECT * FROM organizations WHERE id = ?");
$stmt->execute([$org_id]);
$org = $stmt->fetch();
$title = "Dashboard";
ob_start();
?>
<div class="card" style="max-width:600px;margin:0 auto;">
  <div class="card-body">
    <div class="d-flex align-items-center mb-3">
      <?php if(!empty($org['logo_url'])): ?>
        <img src="<?= htmlspecialchars($org['logo_url']) ?>" alt="Logo" style="height:48px;" class="me-3" />
      <?php endif; ?>
      <div>
        <h5 class="card-title mb-0"><?= htmlspecialchars($org['name']) ?></h5>
        <small class="text-muted"><?= htmlspecialchars($org['address']) ?>, <?= htmlspecialchars($org['zip']) ?> <?= htmlspecialchars($org['city']) ?></small>
      </div>
    </div>
    <ul class="list-group mb-3">
      <li class="list-group-item"><strong>Email:</strong> <?= htmlspecialchars($org['email']) ?></li>
      <li class="list-group-item"><strong>Telefon:</strong> <?= htmlspecialchars($org['phone']) ?></li>
      <li class="list-group-item"><strong>Ansprechpartner:</strong> <?= htmlspecialchars($org['contact_name']) ?></li>
    </ul>
    <a href="/logout" class="btn btn-danger">Logout</a>
  </div>
</div>
<?php
$content = ob_get_clean();
require __DIR__ . '/layout.php';
?>
