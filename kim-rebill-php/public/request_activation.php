<?php
session_start();
require_once __DIR__ . '/../db/db.php';
require_once __DIR__ . '/../mailer/Mailer.php';

$user_id = $_SESSION['user_id'] ?? null;
if (!$user_id) { die('Nicht eingeloggt.'); }

global $pdo;
$stmt = $pdo->prepare("SELECT u.email, u.name, o.name as orgname, o.bexio_org_id FROM users u JOIN organizations o ON u.organization_id = o.id WHERE u.id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();
if (!$user) die('User/Organisation nicht gefunden.');

$to = '<EMAIL>';
$subject = "Zugriffsanfrage: rebill Abo-Aktivierung";
$body = "Organisation: {$user['orgname']}<br>bexio Org-ID: {$user['bexio_org_id']}<br>User: {$user['name']} ({$user['email']})<br><br>Bitte Abo für diese Organisation aktivieren.";

sendMail($to, $subject, $body);
echo "<h2>Anfrage gesendet!</h2><p>Wir melden uns in Kürze.</p>";
?>