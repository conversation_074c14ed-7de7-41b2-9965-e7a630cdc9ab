<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../db/db.php';
session_start();

// Prüfe auf OAuth-Fehler
if (isset($_GET['error'])) {
    $_SESSION['auth_error'] = 'Es ist ein <PERSON>hler passiert: ' . htmlspecialchars($_GET['error']);
    header('Location: /welcome');
    exit;
}

use Jumbojett\OpenIDConnectClient;

// 1. OpenID Connect Client vorbereiten
$oidc = new OpenIDConnectClient(
    'https://auth.bexio.com/realms/bexio',
    'fb7bcebd-1402-4791-b27d-e5f742f01490',
    'AO8Wr1q37jA82R8DMpcpejj_8x49t81RBW5lWGGKawvIBxkZ7eBC7Ixx4LyiS92D7VGdDiA57ASqowoF2AcKnJ4'
);
$oidc->setRedirectURL('https://php.rebill.ch/oauth-callback');
$oidc->addScope('openid profile email company_profile offline_access kb_invoice_edit contact_edit');
$oidc->providerConfigParam([
    'authorization_endpoint' => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/auth',
    'token_endpoint'         => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/token',
    'userinfo_endpoint'      => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/userinfo',
]);

$oidc->authenticate();

$access_token = $oidc->getAccessToken();
$refresh_token = $oidc->getRefreshToken();
$userinfo = $oidc->requestUserInfo();

// --- Firmenprofil holen (nur gewünschte Felder) ---
$ch = curl_init('https://api.bexio.com/3.0/company_profile');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $access_token,
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$profile_response = curl_exec($ch);
curl_close($ch);

$profiles = json_decode($profile_response, true);
$profile = is_array($profiles) && isset($profiles[0]) ? $profiles[0] : [];

$company_id   = $userinfo->company_id ?? '';
$company_name = $profile['name'] ?? '';
$address      = $profile['address'] ?? '';
$zip          = $profile['postcode'] ?? '';
$city         = $profile['city'] ?? '';
$email        = $profile['mail'] ?? '';
$phone        = $profile['phone_fixed'] ?? '';

// Kontaktperson (aus Userinfo)
$contact_name = trim(($userinfo->given_name ?? '') . ' ' . ($userinfo->family_name ?? ''));

// --- Organisation speichern/finden ---
$stmt = $pdo->prepare("SELECT * FROM organizations WHERE bexio_org_id = ?");
$stmt->execute([$company_id]);
$org = $stmt->fetch();
if (!$org) {
    $stmt = $pdo->prepare("
        INSERT INTO organizations
        (name, bexio_org_id, address, zip, city, email, contact_name, phone, refresh_token, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([
        $company_name,
        $company_id,
        $address,
        $zip,
        $city,
        $email,
        $contact_name,
        $phone,
        $refresh_token
    ]);
    $org_id = $pdo->lastInsertId();
} else {
    $org_id = $org['id'];
    $stmt = $pdo->prepare("UPDATE organizations SET contact_name = ?, refresh_token = ? WHERE id = ?");
    $stmt->execute([$contact_name, $refresh_token, $org_id]);
}

// --- User speichern/finden (ein User kann mehrere Organisationen haben) ---
$user_bexio_id = $userinfo->sub ?? '';
$user_name = trim(($userinfo->given_name ?? '') . ' ' . ($userinfo->family_name ?? ''));
$user_email = $userinfo->email ?? '';

// Prüfe, ob dieser User für diese Organisation schon existiert:
$stmt = $pdo->prepare("SELECT * FROM users WHERE bexio_id = ? AND organization_id = ?");
$stmt->execute([$user_bexio_id, $org_id]);
$user = $stmt->fetch();

if (!$user) {
    // Erster User für Orga = Admin, weitere nach Wunsch
    $is_admin = 1;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE organization_id = ?");
    $stmt->execute([$org_id]);
    if ($stmt->fetchColumn() > 0) { $is_admin = 0; }
    $stmt = $pdo->prepare("INSERT INTO users (organization_id, bexio_id, name, email, access_token, is_admin, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
    $stmt->execute([
        $org_id,
        $user_bexio_id,
        $user_name,
        $user_email,
        $access_token,
        $is_admin
    ]);
    $user_id = $pdo->lastInsertId();
} else {
    // User-Daten ggf. aktualisieren
    $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ?, access_token = ? WHERE id = ?");
    $stmt->execute([
        $user_name,
        $user_email,
        $access_token,
        $user['id']
    ]);
    $user_id = $user['id'];
}

// Session setzen und weiterleiten
$_SESSION['user_id'] = $user_id;
$_SESSION['organization_id'] = $org_id;

header("Location: /dashboard");
exit;
?>
