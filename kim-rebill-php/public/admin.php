<?php
require_once __DIR__ . '/../middleware/auth.php';
$user = requireLoginAndFreshToken();
if (!$user['is_admin']) die('Kein Zugriff.');
global $pdo;

// Organisationen
$stmt = $pdo->query("SELECT * FROM organizations");
$orgs = $stmt->fetchAll();

echo "<h1>Adminbereich</h1>";
echo "<table border='1'><tr><th>Name</th><th>beXio ID</th><th>Status</th><th>Intervall</th><th>Aktion</th></tr>";
foreach ($orgs as $o) {
    echo "<form method='post'><tr>";
    echo "<td>" . htmlspecialchars($o['name']) . "</td>";
    echo "<td>" . htmlspecialchars($o['bexio_org_id']) . "</td>";
    echo "<td><select name='status'><option value='trial' " . ($o['subscription_status']=='trial'?'selected':'') . ">Trial</option><option value='active' " . ($o['subscription_status']=='active'?'selected':'') . ">Active</option><option value='inactive' " . ($o['subscription_status']=='inactive'?'selected':'') . ">Inactive</option></select></td>";
    echo "<td><select name='model'><option value='monthly' " . ($o['subscription_model']=='monthly'?'selected':'') . ">Monatlich</option><option value='yearly' " . ($o['subscription_model']=='yearly'?'selected':'') . ">Jährlich</option></select></td>";
    echo "<td><input type='hidden' name='org_id' value='{$o['id']}'><button type='submit'>Speichern</button></td>";
    echo "</tr></form>";
}
echo "</table>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['org_id'])) {
    $stmt = $pdo->prepare("UPDATE organizations SET subscription_status = ?, subscription_model = ? WHERE id = ?");
    $stmt->execute([$_POST['status'], $_POST['model'], $_POST['org_id']]);
    echo "<p>Status aktualisiert!</p>";
}

echo "<p><a href='/dashboard.php'>Zurück</a></p>";
?>