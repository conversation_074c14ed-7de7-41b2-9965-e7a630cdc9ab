<?php
require_once __DIR__ . '/../vendor/autoload.php';

use <PERSON><PERSON>jett\OpenIDConnectClient;

$oidc = new OpenIDConnectClient(
    'https://auth.bexio.com',
    'fb7bcebd-1402-4791-b27d-e5f742f01490',
    'AO8Wr1q37jA82R8DMpcpejj_8x49t81RBW5lWGGKawvIBxkZ7eBC7Ixx4LyiS92D7VGdDiA57ASqowoF2AcKnJ4'
);

$oidc->setRedirectURL('https://php.rebill.ch/oauth/callback');
$oidc->addScope('profile');
$oidc->addScope('email');
$oidc->addScope('company_profile');
$oidc->addScope('offline_access');
$oidc->addScope('kb_invoice_edit');
$oidc->addScope('contact_edit');

// <<< WICHTIG: ALLE Endpunkte manuell setzen!
$oidc->providerConfigParam([
    'authorization_endpoint' => 'https://auth.bexio.com/oauth/authorize',
    'token_endpoint'         => 'https://auth.bexio.com/oauth/access_token',
    'userinfo_endpoint'      => 'https://auth.bexio.com/userinfo',
]);

// Starte Auth-Flow, Weiterleitung zu bexio
$oidc->authenticate();
?>