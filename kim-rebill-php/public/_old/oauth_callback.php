<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../db/db.php';
session_start();

use Jumbojett\OpenIDConnectClient;

$oidc = new OpenIDConnectClient(
    'https://auth.bexio.com',
    '8fbfccfa-2349-462e-93fb-6aaf4ccf29ca',
    'APCta3KhjZIISq4TB-ASJqQxUn10X9zXmwNeSFeMOkO2hpDrYj65gtlca6uivKNWBXlM7leJ9i5pdquN_6XnFd4'
);

$oidc->setRedirectURL('http://localhost:8000/oauth_callback.php');
$oidc->addScope('openid profile email company_profile offline_access kb_invoice_edit contact_edit');

// <<< WICHTIG: ALLE Endpunkte manuell setzen!
$oidc->providerConfigParam([
    'authorization_endpoint' => 'https://auth.bexio.com/oauth/authorize',
    'token_endpoint'         => 'https://auth.bexio.com/oauth/access_token',
    'userinfo_endpoint'      => 'https://auth.bexio.com/userinfo',
]);

// Token abholen & Userinfo
$oidc->authenticate(); // tokens, id_token & userinfo holen

$idToken = $oidc->getIdToken();
$accessToken = $oidc->getAccessToken();
$refreshToken = $oidc->getRefreshToken();
$userinfo = $oidc->requestUserInfo();

$bexio_id = $userinfo->sub ?? '';
$email = $userinfo->email ?? '';
$name = $userinfo->name ?? '';
$org_id_bexio = $userinfo->organization ?? 'default';

// Weiterverarbeitung: Nutzer und Organisationen in deiner DB anlegen/aktualisieren
$stmt = $pdo->prepare("SELECT * FROM organizations WHERE bexio_org_id = ?");
$stmt->execute([$org_id_bexio]);
$org = $stmt->fetch();
if (!$org) {
    $stmt = $pdo->prepare("INSERT INTO organizations (name, bexio_org_id, subscription_start) VALUES (?, ?, NOW())");
    $stmt->execute([$name, $org_id_bexio]);
    $org_id = $pdo->lastInsertId();
} else {
    $org_id = $org['id'];
}

$stmt = $pdo->prepare("SELECT * FROM users WHERE bexio_id = ?");
$stmt->execute([$bexio_id]);
$user = $stmt->fetch();
if (!$user) {
    $stmt = $pdo->prepare("INSERT INTO users (organization_id, bexio_id, access_token, refresh_token, token_expires_at, refresh_token_rotated_at, name, email, is_admin, created_at)
        VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL ? SECOND), NOW(), ?, ?, 0, NOW())");
    $stmt->execute([
        $org_id,
        $bexio_id,
        $accessToken,
        $refreshToken,
        3600, // Default: 1 Stunde gültig (wenn expires_in verfügbar, bitte nutzen)
        $name,
        $email
    ]);
    $user_id = $pdo->lastInsertId();
} else {
    $stmt = $pdo->prepare("UPDATE users SET access_token = ?, refresh_token = ?, token_expires_at = DATE_ADD(NOW(), INTERVAL ? SECOND), refresh_token_rotated_at = NOW(), name = ?, email = ? WHERE id = ?");
    $stmt->execute([
        $accessToken,
        $refreshToken,
        3600, // Default
        $name,
        $email,
        $user['id']
    ]);
    $user_id = $user['id'];
}

$_SESSION['user_id'] = $user_id;
$_SESSION['organization_id'] = $org_id;

header("Location: /dashboard.php");
exit;
?>
