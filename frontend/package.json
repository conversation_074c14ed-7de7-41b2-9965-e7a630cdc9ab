{"name": "rebill", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 80", "lint": "next lint"}, "dependencies": {"@mantine/core": "^7.8.1", "@mantine/dates": "^7.8.1", "@mantine/hooks": "^7.8.1", "@mantine/modals": "^7.8.1", "@mantine/notifications": "^7.8.1", "@mantine/nprogress": "^7.8.1", "accept-language": "^3.0.18", "axios": "^1.6.0", "debug": "^4.3.4", "encoding": "^0.1.13", "firebase": "^10.5.0", "i18next": "^23.6.0", "i18next-browser-languagedetector": "^7.1.0", "i18next-locize-backend": "^6.2.3", "i18next-resources-to-backend": "^1.1.4", "next": "^14.2.3", "react": "^18", "react-cookie": "^6.1.1", "react-countup": "^6.5.0", "react-dom": "^18", "react-i18next": "^13.3.1", "react-type-animation": "^3.2.0", "supports-color": "^9.4.0", "tabler-icons-react": "^1.56.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}}