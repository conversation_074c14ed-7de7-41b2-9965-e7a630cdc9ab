// import React from 'react'
// import { MantineProvider, ColorSchemeScript } from '@mantine/core';
// import { Metadata } from 'next';
// import { DefaultHeader } from '@/components/global/Headers';
// import { theme } from '@/utils/theme/theme';
// import { Notifications } from '@mantine/notifications';
// import LangProvider from '@/providers/Lang.provider';

// export const metadata: Metadata = {
//     title: 'reBill | Recurring billing made simple',
//     description: 'Recurring billing made simple',
// }

// export default async function RootLayout({
//     children,
// }: { children: React.ReactNode, params: { lng: string } }) {
//     return (
//         <html lang={"en-US"}>
//             <head>
//                 <ColorSchemeScript />
//             </head>
//             <body>
//                 <LangProvider>
//                     <MantineProvider theme={theme}>
//                         <Notifications position="top-center" styles={{ notification: { border: "1px solid rgba(0,0,0,.1)" } }} />
//                         {children}
//                     </MantineProvider>
//                 </LangProvider>
//             </body>
//         </html>
//     )
// }

"use client"
import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dates/styles.css';
import './globals.css'
import DashboardLayoutController from '@/components/dashboard/DashboardLayoutController'
import AuthGuard from '@/components/global/AuthGuard'
import BexioDataProvider from '@/providers/BexioDataProvider'
import DraftsProvider from '@/providers/DraftsProvider'
import InvoicesProvider from '@/providers/InvoicesProvider'
import LangProvider from '@/providers/Lang.provider'
import RecurringInvoiceProvider from '@/providers/RecurringInvoiceProvider'
import { theme } from '@/utils/theme/theme'
import { ColorSchemeScript, MantineProvider } from '@mantine/core'
import { Notifications } from '@mantine/notifications'
import { usePathname } from 'next/navigation'
import React, { PropsWithChildren } from 'react'

export default function DashboardLayout({ children }: PropsWithChildren) {
    const pathname = usePathname()

    return (
        <html lang={"en-US"}>
            <head>
                <ColorSchemeScript />
            </head>
            <body>
                <LangProvider>
                    <MantineProvider theme={theme}>
                        <Notifications position="top-center" styles={{ notification: { border: "1px solid rgba(0,0,0,.1)" } }} />
                        {pathname.startsWith("/auth") || pathname.startsWith("/setup") || pathname.startsWith("/oauth")
                            ? <>{children}</>
                            : <AuthGuard>
                                <RecurringInvoiceProvider>
                                    <DraftsProvider>
                                        <InvoicesProvider>
                                            <BexioDataProvider>
                                                <DashboardLayoutController>
                                                    {children}
                                                </DashboardLayoutController>
                                            </BexioDataProvider>
                                        </InvoicesProvider>
                                    </DraftsProvider>
                                </RecurringInvoiceProvider>
                            </AuthGuard>
                        }
                    </MantineProvider>
                </LangProvider>
            </body>
        </html>
    )
}
