"use client"
import api from '@/utils/api/api'
import { Box, Button, Divider, Image, Loader, Paper, Text } from '@mantine/core'
import { useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import { X } from 'tabler-icons-react'

export default function Page() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [code, setCode] = useState<string | null | undefined>(undefined)
    const [error, setError] = useState("")

    useEffect(() => {
        const c = searchParams.get("code")
        setCode(c || null)
    }, [searchParams])

    useEffect(() => {
        if (!code) return;
        api("/oauth/bexio/authorize", {
            code
        }).then((res) => {
            if (res.error) return setError(res.msg);
            router.push("/setup")
        })
    }, [code])

    return (
        <Box className='flex aic jcc' py={25}>
            <Paper px="md" py="xl" shadow='xs' className='flex aic fdc' w="100%" maw={400} mx="auto" style={{ borderTop: "3px solid #000" }}>
                <Box className='flex aic' style={{ gap: 25 }}>
                    <Image src={"/logo/icon.ico"} style={{ width: 30, height: 30 }} />
                    {error ? <X color='var(--mantine-color-red-6)' size={20} /> : <Loader size={"xs"} type='bars' />}
                    <Image src={"/logo/bexio.png"} style={{ width: 30, height: 30 }} />
                </Box>
                <Box mt={20} className='flex aic fdc'>
                    <Text fw={600}>{error ? "Error" : "Linking your account"}</Text>
                    <Text fz="sm" c="dimmed">{error || "Please wait while we securly link your Bexio account"}</Text>
                    {error ? <Button variant='default' mt={10} onClick={() => router.push("/")}>Back</Button> : null}
                </Box>
            </Paper>
        </Box>
    )
}
