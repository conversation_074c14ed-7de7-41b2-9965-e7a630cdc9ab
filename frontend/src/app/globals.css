h1,
h2,
h3,
h4,
h5,
h6,
p {
    margin: 0;
}

a {
    text-decoration: none;
    color: inherit;
}

.flex {
    display: flex;
}

.aic {
    align-items: center;
}

.jcc {
    justify-content: center;
}

.fdc {
    flex-direction: column;
}

.jcsb {
    justify-content: space-between;
}

.fdr {
    flex-direction: row;
}

.cp {
    cursor: pointer;
}

.background-grid {
    background-image: linear-gradient(#f1f1f1 0.5px,
            transparent 0.5px,
            transparent calc(100% - 0.5px),
            #f1f1f1 calc(100% - 0.5px)),
        linear-gradient(90deg,
            #f1f1f1 0.5px,
            transparent 0.5px,
            transparent calc(100% - 0.5px),
            #f1f1f1 calc(100% - 0.5px));
    background-size: 10% 20%;
}