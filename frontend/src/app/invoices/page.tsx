"use client"
import CreateInvoiceModal from '@/components/dashboard/InvoiceModals'
import { Stat } from '@/components/dashboard/Stat'
import { BexioDataContext } from '@/context/BexioData.contetx'
import { RecurringInvoicesContext } from '@/context/RecurringInvoices.context'
import api from '@/utils/api/api'
import { getMRR, getYRR } from '@/utils/data/getRecurringRevenue'
import { ActionIcon, Avatar, Badge, Box, Button, Collapse, CopyButton, Divider, Loader, Menu, Modal, Paper, SegmentedControl, Skeleton, Text, Tooltip } from '@mantine/core'
import { useHover } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import React, { useContext, useEffect, useState } from 'react'
import { Check, ChevronDown, Copy, Edit, FileX, History, Minus, Package, Plus, Receipt, Trash, User, UserOff, X } from 'tabler-icons-react'
import { BexioContact, LiteCustomerDetails, RecurringInvoice } from '../../../../types/global'
import CountUp from 'react-countup';
// const CopyIcon = ({ value }: { value: string }) => {
//     return (
//         <CopyButton value={value} timeout={2000}>
//             {({ copied, copy }) => (
//                 <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position="right">
//                     <ActionIcon color={copied ? 'teal' : 'gray'} variant="subtle" onClick={copy}>
//                         {copied ? (
//                             <Check style={{ width: 16 }} />
//                         ) : (
//                             <Copy style={{ width: 16 }} />
//                         )}
//                     </ActionIcon>
//                 </Tooltip>
//             )}
//         </CopyButton>
//     )
// }

// const RecurringInvoiceModal = ({ inv, close }: { inv: RecurringInvoice, close: () => void }) => {
//     const bexioData = useContext(BexioDataContext)
//     const [contact, setContact] = useState<BexioContact | undefined>(undefined)
//     console.log(bexioData)
//     useEffect(() => {
//         api("/bexio/get-contact", {
//             id: inv.customerID
//         }).then(res => {
//             if (res.error) {
//                 return notifications.show({
//                     color: "red",
//                     title: "Error",
//                     message: res.msg
//                 })
//             }
//             setContact(res.data)
//         })
//     }, [])

//     return (
//         <div className='flex aic jcc'>
//             <Box maw={900} w={"100%"} h={200}>
//                 <div className='flex aic jcsb' style={{ width: "100%" }}>
//                     <div className='flex aic' style={{ gap: 5 }}>
//                         <Receipt size={20} />
//                         <Text fw={600}>Invoice {inv.id}</Text>
//                     </div>
//                     <ActionIcon onClick={close} variant={"subtle"} color="gray"><X color='#000' size={20} /></ActionIcon>
//                 </div>
//                 <Divider my={20} />
//                 <div style={{ marginTop: 10 }}>
//                     <div className='flex aic jcsb'>
//                         <div>
//                             <Badge size={"sm"} color={inv.status === "active" ? "green" : "gray"} variant="light">{inv.status === "active" ? "Active" : "In-Active"}</Badge>
//                             <Text fw={600} fz={25}>{inv.title}</Text>
//                             <Text fz={"sm"} c="dimmed">Billed {inv.period} to customer <Text span>#{inv.customerID}</Text></Text>
//                         </div>
//                         <Menu withArrow position="bottom-end">
//                             <Menu.Target>
//                                 <Button variant={"default"} leftSection={<ChevronDown size={18} />}>Actions</Button>
//                             </Menu.Target>
//                             <Menu.Dropdown>
//                                 <Menu.Item leftSection={<Edit size={18} />}>Edit Invoice</Menu.Item>
//                                 <Menu.Item disabled leftSection={<Receipt size={18} />}>View invoice in Bexio</Menu.Item>
//                                 <Menu.Item disabled leftSection={<FileX size={18} />}>Void latest invoice</Menu.Item>
//                                 <Menu.Divider />
//                                 <Menu.Item color={"red"} fw={500} leftSection={<Trash size={18} />}>Cancel Recurring Billing</Menu.Item>
//                             </Menu.Dropdown>
//                         </Menu>
//                     </div>
//                     <div>
//                         <div>
//                             <div style={{ marginTop: 30 }}>
//                                 <div className='flex aic jcsb'>
//                                     <div className='flex aic' style={{ gap: 10 }}>
//                                         <History size={20} />
//                                         <Text fw={600}>Billing History</Text>
//                                     </div>
//                                     <Text fz="sm" c='dimmed'>Next charge: <Text span fw={500} fz="sm">{new Date(inv.nextCharge).toLocaleDateString()}</Text></Text>
//                                 </div>
//                                 <Divider variant={"dashed"} my={15} />
//                                 <div >
//                                     {!inv.history?.length && inv.status !== "active"
//                                         ? <Text c="dimmed" ta="center" py={20}>No billing history to display</Text>
//                                         : <div className='flex fdc' style={{ gap: 15 }}>
//                                             <div className='flex' style={{ gap: 15 }}>
//                                                 <ActionIcon variant={"light"} color="gray">
//                                                     <Minus />
//                                                 </ActionIcon>
//                                                 <div>
//                                                     <Text fw={500}>Next Invoice</Text>
//                                                     <Text fw={500} c="dimmed" fz={"xs"}>{new Date(inv.nextCharge).toString()}</Text>
//                                                 </div>
//                                             </div>
//                                             {inv.history.sort((a, b) => {
//                                                 return (b.rebill_timestamp || (b.error?.timestamp as number) - a.rebill_timestamp || (a.error?.timestamp as number))
//                                             }).map((inv, i) => {
//                                                 return (
//                                                     <>
//                                                         <Box key={i} bg={""} w="100%">
//                                                             {inv.error
//                                                                 ? <div className='flex' style={{ gap: 15 }}>
//                                                                     <ActionIcon variant={"light"} color="red">
//                                                                         <X />
//                                                                     </ActionIcon>
//                                                                     <div>
//                                                                         <Text fw={500}>{inv.error.message}</Text>
//                                                                         <Text fw={500} c="dimmed" fz={"xs"}>{new Date(inv.error.timestamp).toString()}</Text>
//                                                                     </div>
//                                                                 </div>
//                                                                 : <div className='flex' style={{ gap: 15 }}>
//                                                                     <ActionIcon variant={"light"} color="green">
//                                                                         <Check />
//                                                                     </ActionIcon>
//                                                                     <div>
//                                                                         <Text fw={500}>Invoice was sent successfully</Text>
//                                                                         <Text fw={500} c="dimmed" fz={"xs"}>{new Date(inv.rebill_timestamp).toString()}</Text>
//                                                                     </div>
//                                                                 </div>
//                                                             }
//                                                         </Box>
//                                                     </>
//                                                 )
//                                             })}
//                                         </div>
//                                     }
//                                 </div>
//                             </div>
//                             <div style={{ marginTop: 30 }}>
//                                 <div className='flex aic jcsb'>
//                                     <div className='flex aic' style={{ gap: 10 }}>
//                                         <Package size={20} />
//                                         <Text fw={600}>Items</Text>
//                                     </div>
//                                     <Button leftSection={<Edit size={20} />} variant="subtle" size={"xs"}>Edit</Button>
//                                     {/* <Text fz="sm" c='dimmed'>Next charge: <Text span fw={500} fz="sm">{new Date(inv.nextCharge).toLocaleDateString()}</Text></Text> */}
//                                 </div>
//                                 {/* <Divider variant={"dashed"} my={15} /> */}
//                                 <div style={{ borderRadius: 10, overflow: "hidden" }}>
//                                     <Paper radius={0} style={{ borderTopLeftRadius: 10, borderTopRightRadius: 10 }} bg="#f9f9f9" p="sm" mt={10} className='flex aic jcsb'>
//                                         <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Name</Text>
//                                         <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Quantity</Text>
//                                         <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Tax</Text>
//                                         <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Unit Name</Text>
//                                         <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Unit Price</Text>
//                                         <Text fz="sm" fw={600} c="dimmed">Total</Text>
//                                     </Paper>
//                                     {inv.items.map((item, i) => {
//                                         console.log(item)
//                                         return (
//                                             <>
//                                                 {/* {i !== 0 */}
//                                                 <Divider opacity={.5} />
//                                                 {/* : null */}
//                                                 {/* } */}
//                                                 <Paper radius={0} bg="#f9f9f9" p="sm" className='flex aic jcsb'>
//                                                     <Text fz="sm" lineClamp={1} style={{ flex: 1 }} fw={500}>{item.name}</Text>
//                                                     <Text fz="sm" style={{ flex: 1 }} fw={500}>{item.unitQ}</Text>
//                                                     <Text fz="sm" style={{ flex: 1 }} fw={500}>{bexioData.taxes?.find((t) => t?.id)?.display_name}</Text>
//                                                     <Text fz="sm" style={{ flex: 1 }} fw={500}>{bexioData.units?.find((t) => t?.id)?.name}</Text>

//                                                     <Text fz="sm" style={{ flex: 1 }} fw={500}>{item.unitPrice}</Text>
//                                                     <Text fz="sm" fw={500}>{item.unitPrice}</Text>
//                                                 </Paper>
//                                             </>

//                                         )
//                                     })}
//                                 </div>
//                             </div>

//                             <div style={{ marginTop: 30 }}>
//                                 <div className='flex aic' style={{ gap: 10 }}>
//                                     <User size={20} />
//                                     <Text fw={600}>Customer</Text>
//                                 </div>
//                                 <Divider variant={"dashed"} my={15} />
//                                 <div>
//                                     {contact
//                                         ? <Box className='flex' style={{ gap: 15 }}>
//                                             {contact.profile_image
//                                                 ? <Image style={{ borderRadius: 10 }} alt='Customer profile picture' width={45} height={45} src={"data:image/png;base64," + contact.profile_image} />
//                                                 : <Avatar size={45} />
//                                             }
//                                             <div>
//                                                 <Text lineClamp={1} fw={500}>{contact.name_1} {contact.name_2}</Text>
//                                                 <Text style={{ position: "relative", bottom: 6 }} fz={"sm"} c="dimmed">{contact.mail}{contact.phone_fixed ? <><Text fz={"xl"} px={8} span>{"•"}</Text>{contact.phone_fixed}</> : ""}</Text>
//                                             </div>
//                                         </Box>
//                                         : <div className='flex aic jcc' style={{ padding: "20px 0" }}>
//                                             <Loader size={"sm"} />
//                                         </div>
//                                     }
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 </div>
//             </Box>
//         </div>
//     )
// }

// const Invoice = ({ inv, i }: { inv: RecurringInvoice, i: number }) => {
//     const { hovered, ref } = useHover()
//     return (
//         <>
//             {i !== 0
//                 ? <Divider variant="dashed" />
//                 : null
//             }
//             <Link href={`/dashboard/invoices/${inv.id}`}>
//                 <Box ref={ref} style={{ background: hovered ? "#f9f9f9" : "#fff", transition: "all .15s", padding: `10px` }} py={10} className='flex aic jcsb cp'>
//                     <Text fz={"sm"} style={{ flex: 1 }}>{inv.title}</Text>
//                     <div style={{ flex: 1 }}>
//                         {inv.DELETED
//                             ? <Badge size={"sm"} color={"red"} variant="light">Deleted</Badge>
//                             : <Badge size={"sm"} color={inv.status === "active" ? "green" : "gray"} variant="light">{inv.status === "active" ? "Active" : "Inactive"}</Badge>
//                         }
//                         {/* <Text fz={"xs"} c="dimmed">#{inv.status}</Text> */}
//                         {/* <Text fz={"sm"}>Latest Invoice: {inv.invoiceID ? <Text span c="blue" fz={"sm"} fw={500} style={{ textDecoration: "underline" }} className="cp">{inv.invoiceID}</Text> : <Text span fz="sm" fw={500} c="dimmed">Not billed yet</Text>}</Text> */}
//                     </div>
//                     <Text fz={"sm"} style={{ flex: 1 }}>{new Date(inv.nextCharge).toDateString()}</Text>
//                     <Text fz={"sm"} style={{ flex: 1 }}>{inv.period.substring(0, 1).toUpperCase() + inv.period.substring(1, inv.period.length)}</Text>
//                 </Box>
//             </Link>
//         </>
//     )
// }

type Customers = {
    [key in string]: Customer
}

type Customer = {
    info: LiteCustomerDetails,
    invoies: Array<RecurringInvoice>
}

const CustomerBreakdown = ({ customer }: { customer: Customer }) => {
    const [expanded, setExpanded] = useState(true)

    const Field = ({ label, value, nf }: { label: string, value: any, nf?: boolean }) => {
        return (
            <div style={{ flex: nf ? .5 : 1 }}>
                <Text fw={500} c="dimmed" fz="sm">{label}</Text>
                {typeof value === "string"
                    ? <Text fz="sm" style={{ textTransform: "capitalize" }} fw={500}>{value}</Text>
                    : <>{value}</>
                }

            </div>
        )
    }
    return (
        <Box>
            <Box className='flex aic jcsb'>
                <div className='flex' style={{ gap: 10 }}>
                    <Avatar src={"data:image/png;base64," + customer.info.pfp} radius={10} />
                    <div>
                        <Text fw={500}>{customer.info.name}</Text>
                        <Text fz="sm" c="dimmed">{customer.info.email}</Text>
                    </div>
                </div>
                <div className='flex aic' style={{ gap: 10 }}>
                    <Text fw={500}>YRR: CHF{getYRR(customer.invoies)}</Text>
                    <Divider mx={5} orientation="vertical" />
                    <ActionIcon onClick={() => setExpanded(!expanded)} variant={"subtle"}><ChevronDown size={20} style={{
                        transition: "all .3s",
                        transform: `rotate(${expanded ? 0 : 180}deg)`
                    }} /></ActionIcon>
                </div>
            </Box>
            <Collapse in={expanded}>
                <Paper mt={10} className="flex fdc" radius={"md"} style={{ gap: 0 }}>
                    {customer.invoies.map((inv, i) => {
                        return (
                            <>
                                {i !== 0
                                    ? <Divider opacity={.5} />
                                    : null
                                }
                                <Box p="sm" className='flex aic jcsb' pos={"relative"}>
                                    <Link href={`/invoices/${inv.id}`} style={{ flex: 1 }} className='flex aic jcsb'>
                                        <Field nf label='Status' value={<>
                                            {inv.DELETED
                                                ? <Badge size={"sm"} color={"red"} variant="light">Deleted</Badge>
                                                : <Badge size={"sm"} color={inv.status === "active" ? "green" : "gray"} variant="light">{inv.status === "active" ? "Active" : "Inactive"}</Badge>
                                            }
                                        </>} />
                                        <Field label='Next Charge' value={new Date(inv.nextCharge).toDateString()} />
                                        <Field label='Description' value={inv.rebill_description} />
                                        <Field label='Total Price' value={`CHF${inv.items.map((i) => i.unitPrice * i.unitQ).reduce((a, b) => a + b, 0).toLocaleString()}`} />
                                        <Field label='Billing Period' value={inv.period} />
                                    </Link>
                                    <div style={{ justifyContent: "flex-end" }} className="flex">
                                        <Link href={`/invoices/${inv.id}/edit`}>
                                            <ActionIcon variant={"subtle"}>
                                                <Edit size={20} />
                                            </ActionIcon>
                                        </Link>
                                    </div>
                                </Box>
                            </>
                        )
                    })}
                </Paper>
            </Collapse>
        </Box>
    )
}

const RecurringInvoices = () => {
    const recurringInvoices = useContext(RecurringInvoicesContext)
    const [customers, setCustomers] = useState<Customers>(new Object() as Customers)

    useEffect(() => {
        const newCustomers: Customers = new Object() as Customers

        for (let i = 0; i < recurringInvoices.length; i++) {
            if (newCustomers[recurringInvoices[i].customerID]) {
                newCustomers[recurringInvoices[i].customerID].invoies.push(recurringInvoices[i])
                // We want to display the most up to date customer info. Lite customer info is sotred inside each invoice. We'll set the info field to the most recently updated invoice's lite customer data
                if (newCustomers[recurringInvoices[i].customerID].invoies.sort((a, b) => b.updated - a.updated)[0].id === recurringInvoices[i].id) {
                    newCustomers[recurringInvoices[i].customerID].info = recurringInvoices[i].customerDetails
                }
            } else {
                newCustomers[recurringInvoices[i].customerID] = {
                    info: { ...recurringInvoices[i].customerDetails },
                    invoies: [{ ...recurringInvoices[i] }]
                }
            }
        }

        setCustomers(newCustomers)

    }, [])

    return (
        <>

            <div style={{ marginTop: 15, gap: 10 }} className="flex">
                <Stat dark label='Total YRR' value={<>
                    CHF{getYRR(recurringInvoices).split(",").map((num, i) => {
                        return (
                            <>
                                <CountUp
                                    end={parseInt(num)}
                                />
                                {getYRR(recurringInvoices).split(",")[i + 1]
                                    ? ","
                                    : null
                                }

                            </>
                        )
                    })}
                </>} />

                <Stat dark label='Total MRR' value={
                    <>
                        CHF{getMRR(recurringInvoices).split(",").map((num, i) => {
                            return (
                                <>
                                    <CountUp
                                        end={parseInt(num)}
                                    />
                                    {getMRR(recurringInvoices).split(",")[i + 1]
                                        ? ","
                                        : null
                                    }

                                </>
                            )
                        })}
                    </>
                } />
                <Stat dark label='Customers' value={Object.keys(customers).length} />
                <Stat dark label='Recurring Invoices' value={recurringInvoices.length} />
            </div>
            <Paper bg={"gray.0"} p="lg" radius={"lg"} className="flex fdc" mt={20}>
                <Box className='flex fdc' style={{ gap: 25 }}>
                    {Object.keys(customers).map((customerID) => {
                        const customer = customers[customerID]
                        return (
                            <CustomerBreakdown customer={customer} />
                        )
                    })}
                    {!Object.keys(customers).length
                        ? <div className='flex aic jcc fdc' style={{ padding: "30px 0" }}>
                            <UserOff color='var(--mantine-color-dimmed)' />
                            <Text ta={"center"} c="dimmed">You don't have any recurring invoices yet</Text>
                        </div>
                        : null
                    }
                </Box>
            </Paper>
        </>
    )
}

export default function Page() {
    return (
        <>
            <title>Invoices | reBill</title>
            <div>
                <div className='flex aic jcsb'>
                    <div>
                        <Text fw={800} fz={40} style={{ lineHeight: 1 }}>
                            <Text fw={800} fz={20} c="dimmed">
                                Recurring
                            </Text>
                            Invoices
                        </Text>
                    </div>
                    {/* @ts-ignore */}
                    <div>
                        <CreateInvoiceModal target={<Button leftSection={<Plus size={20} />}>Create New</Button>} />

                    </div>
                    {/* <SegmentedControl value={mode} onChange={setMode} data={[{ label: "Recurring", value: "recurring" }, { label: "Bexio", value: "bexio" }]} /> */}
                </div>

                <RecurringInvoices />
            </div >
        </>
    )
}
