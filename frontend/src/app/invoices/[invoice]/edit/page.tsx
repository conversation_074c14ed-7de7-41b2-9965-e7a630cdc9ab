"use client"
import { InvoiceEditorForm } from '@/components/dashboard/InvoiceModals'
import Error from '@/components/global/Error'
import { InvoicePageContext } from '@/context/InvoicePage.context'
import api from '@/utils/api/api'
import { ActionIcon, Button, Text } from '@mantine/core'
import { useRouter } from 'next/navigation'
import React, { useContext, useState } from 'react'
import { ArrowLeft, FileInvoice, Receipt, Trash } from 'tabler-icons-react'

export default function Page() {
    const { contact, invoice } = useContext(InvoicePageContext)
    const router = useRouter()
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState("")
    return (
        <InvoiceEditorForm close={() => router.back()} draftData={invoice} customAction={(newInvoiceDetails) => (
            <>
                <div className='flex aic' style={{ gap: 10 }}>
                    <Button loading={loading} size={"md"} leftSection={<Receipt size={22} />} onClick={() => {
                        setError("")
                        setLoading(true)
                        api("/bexio/update-invoice", {
                            invoiceID: invoice.id,
                            newInvoiceData: {
                                ...newInvoiceDetails
                            }
                        }).then((res) => {
                            setLoading(false)
                            if (res.error) {
                                return setError(res.msg)
                            }
                            window.location.replace(
                                `/invoices/${invoice.id}`
                            )
                        })
                    }}>Save Changes</Button>
                    <Button variant={"default"} size="md" leftSection={<Trash size={20} />}>Discard Changes</Button>
                </div>
                <Error mt={15}>{error}</Error>
            </>

        )} customHeader={() => (
            <div className='flex aic' style={{ width: "100%", gap: 10 }}>
                <ActionIcon onClick={() => router.back()} variant={"subtle"} color="gray"><ArrowLeft color='#000' size={20} /></ActionIcon>
                <div className='flex aic' style={{ gap: 8 }}>
                    <Receipt size={20} />
                    <Text fw={600}>Editing recurring invoice ({invoice.id})</Text>
                </div>
            </div>
        )} />
    )
}
