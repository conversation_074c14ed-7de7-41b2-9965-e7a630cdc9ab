"use client"
import { Loader } from '@mantine/core'
import React, { useContext, useState } from 'react'
import { BexioInvoice, RecurringInvoice } from '../../../../../types/global'
import { BexioDataContext } from '@/context/BexioData.contetx'
import api from '@/utils/api/api'
import { ActionIcon, Avatar, Badge, Box, Button, Divider, Menu, Paper, Text, Tooltip } from '@mantine/core'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Check, ChevronDown, ChevronUp, Edit, FileX, History, Minus, Package, Percentage, PlayerPause, PlayerPlay, Receipt, Trash, User, X } from 'tabler-icons-react'
import Link from 'next/link'
import { InvoicePageContext } from '@/context/InvoicePage.context'

const PauseBilling = ({ invoiceData }: { invoiceData: RecurringInvoice }) => {
    const [loading, setLoading] = useState(false)

    return (
        <Menu.Item disabled={loading || invoiceData.status === "in-active" || !!invoiceData.DELETED} leftSection={loading ? <Loader size={"xs"} /> : <PlayerPause size={18} />} onClick={() => {
            setLoading(true)
            api("/bexio/pause-invoice", {
                invoiceID: invoiceData.id
            }).then(() => {
                setLoading(false)
            })
        }}>Pause Billing</Menu.Item>
    )
}

const StartBilling = ({ invoiceData }: { invoiceData: RecurringInvoice }) => {
    const [loading, setLoading] = useState(false)

    return (
        <Menu.Item disabled={loading || invoiceData.status === "active" || !!invoiceData.DELETED} leftSection={loading ? <Loader size={"xs"} /> : <PlayerPlay size={18} />} onClick={() => {
            setLoading(true)
            api("/bexio/start-invoice", {
                invoiceID: invoiceData.id
            }).then(() => {
                setLoading(false)
            })
        }}>Start Billing</Menu.Item>
    )
}

const DeleteBilling = ({ invoiceData }: { invoiceData: RecurringInvoice }) => {
    const [loading, setLoading] = useState(false)

    return (
        <Menu.Item color={"red"} fw={500} leftSection={loading ? <Loader size={"xs"} /> : <Trash size={18} />} onClick={() => {
            setLoading(true)
            api("/bexio/delete-invoice", {
                invoiceID: invoiceData.id,
            }).then(() => {
                setLoading(false)
            })
        }}>Delete Recurring Billing</Menu.Item>
    )
}

const BillingHistory = () => {
    const { invoice: inv } = useContext(InvoicePageContext)
    const [expanded, setExpanded] = useState(false)

    return (
        <>
            <div className='flex aic jcsb'>
                <div className='flex aic' style={{ gap: 10 }}>
                    <History size={20} />
                    <Text fw={600}>Billing History</Text>
                </div>
                <Box className='flex aic' style={{ gap: 10 }}>
                    {!inv.DELETED && inv.status !== "in-active"
                        ? <Text fz="sm" c='dimmed'>Next charge: <Text span fw={500} fz="sm">{new Date(inv.nextCharge).toLocaleDateString()}</Text></Text>
                        : null
                    }
                    <Tooltip label={expanded ? "Show Less" : "Show More"}>
                        <ActionIcon onClick={() => setExpanded(!expanded)} variant="default" size={"md"} radius={10} style={{ transition: "all .2s", transform: `rotate(${expanded ? 180 : 0}deg)` }}>
                            <ChevronDown size={20} />
                        </ActionIcon>

                    </Tooltip>
                </Box>
            </div>
            <Divider variant={"dashed"} my={15} />
            <div>
                {inv.DELETED
                    ? <div className='flex' style={{ gap: 15, marginBottom: 15 }}>
                        <ActionIcon variant={"light"} color="red">
                            <Trash />
                        </ActionIcon>
                        <div>
                            <Text fw={500}>Recurring billing cancelled</Text>
                            <Text fw={500} c="dimmed" fz={"xs"}>Billing cancelled on {new Date(inv.DELETED).toString()}</Text>
                        </div>
                    </div>
                    : null
                }
                {!inv.history?.length && inv.status !== "active" && !inv.DELETED
                    ? <Text c="dimmed" ta="center" py={20}>No billing history to display</Text>
                    : <div className='flex fdc' style={{ gap: 15 }}>
                        <div className='flex' style={{ gap: 15 }}>
                            <ActionIcon variant={"light"} color="gray">
                                <Minus />
                            </ActionIcon>
                            <div>
                                <Text fw={500}>Next Invoice</Text>
                                <Text fw={500} c="dimmed" fz={"xs"}>{new Date(inv.nextCharge).toString()}</Text>
                            </div>
                        </div>
                        {inv.history.sort((a, b) => {
                            return (a.rebill_timestamp || (a.error?.timestamp as number) - b.rebill_timestamp || (b.error?.timestamp as number))
                        }).map((inv, i) => {
                            if (!expanded && i > 3) return null;
                            return (
                                <>
                                    <Box key={i} bg={""} w="100%">
                                        {inv.error
                                            ? <div className='flex' style={{ gap: 15 }}>
                                                <ActionIcon variant={"light"} color="red">
                                                    <X />
                                                </ActionIcon>
                                                <div>
                                                    <Text fw={500}>{inv.error.message}</Text>
                                                    <Text fw={500} c="dimmed" fz={"xs"}>{new Date(inv.error.timestamp).toString()}</Text>
                                                </div>
                                            </div>
                                            : <div className='flex' style={{ gap: 15 }}>
                                                <ActionIcon variant={"light"} color="green">
                                                    <Check />
                                                </ActionIcon>
                                                <div>
                                                    <Text fw={500}>Invoice was sent successfully</Text>
                                                    <Text fw={500} c="dimmed" fz={"xs"}>{new Date(inv.rebill_timestamp).toString()}</Text>
                                                </div>
                                            </div>
                                        }
                                    </Box>
                                </>
                            )
                        })}
                    </div>
                }
            </div>
        </>
    )
}

export default function Invoice() {
    const { invoice: inv, contact } = useContext(InvoicePageContext)
    const bexioData = useContext(BexioDataContext)
    const router = useRouter()

    return (
        <>
            <title>Invoice | reBill</title>
            <div className='flex aic jcc fdc'>
                <Box maw={1000} w={"100%"} h={200}>
                    <div className='flex aic jcsb'>
                        <div className='flex aic' style={{ width: "100%", gap: 10 }}>
                            <ActionIcon onClick={() => router.back()} variant={"subtle"} color="gray"><ArrowLeft color='#000' size={20} /></ActionIcon>
                            <div className='flex aic' style={{ gap: 5 }}>
                                <Receipt size={20} />
                                <Text fw={600}>Invoice {inv.id}</Text>
                            </div>
                        </div>
                        <Menu disabled={!!inv.DELETED} closeOnItemClick={false} withArrow position="bottom-end">
                            <Menu.Target>
                                <Button variant={"default"} leftSection={<ChevronDown size={18} />}>Actions</Button>
                            </Menu.Target>
                            <Menu.Dropdown>
                                <Link href={`/invoices/${inv.id}/edit`}>
                                    <Menu.Item leftSection={<Edit size={18} />}>Edit Invoice</Menu.Item>
                                </Link>
                                <Menu.Item disabled={!inv.invoiceID} leftSection={<Receipt size={18} />} onClick={() => window.open(`https://office.bexio.com/index.php/kb_invoice/show/id/${inv.invoiceID}`)}>View invoice in Bexio</Menu.Item>
                                <Menu.Item disabled leftSection={<FileX size={18} />}>Void latest invoice</Menu.Item>
                                <PauseBilling invoiceData={inv} />
                                <StartBilling invoiceData={inv} />
                                {/* <Menu.Item leftSection={<PlayerPause size={18} />}>Pause Billing</Menu.Item> */}
                                <Menu.Divider />
                                <DeleteBilling invoiceData={inv} />
                                {/* <Menu.Item color={"red"} fw={500} leftSection={<Trash size={18} />}>Delete Recurring Billing</Menu.Item> */}
                            </Menu.Dropdown>
                        </Menu>
                    </div>
                    <Divider opacity={.5} my={15} />
                    <div style={{ marginTop: 0 }}>
                        <div className='flex aic jcsb'>
                            <div>
                                {inv.DELETED
                                    ? <Badge size={"sm"} color={"red"} variant="light">Deleted</Badge>
                                    : <Badge size={"sm"} color={inv.status === "active" ? "green" : "gray"} variant="light">{inv.status === "active" ? "Active" : "Inactive"}</Badge>
                                }

                                <Text fw={600} fz={25}>{inv.title}</Text>
                                <Text fz={"sm"} c="dimmed">Billed {inv.period} to customer <Text span>#{inv.customerID}</Text></Text>
                            </div>
                        </div>
                        <div>
                            <div>
                                <div style={{ marginTop: 30 }}>
                                    <BillingHistory />

                                </div>
                                <div style={{ marginTop: 30 }}>
                                    <div className='flex aic jcsb'>
                                        <div className='flex aic' style={{ gap: 10 }}>
                                            <Package size={20} />
                                            <Text fw={600}>Items</Text>
                                        </div>
                                        <Link href={`/invoices/${inv.id}/edit`}>
                                            <Button disabled={!!inv.DELETED} leftSection={<Edit size={20} />} variant="subtle" size={"xs"}>Edit</Button>
                                        </Link>
                                    </div>
                                    {/* <Divider variant={"dashed"} my={15} /> */}
                                    <div style={{ borderRadius: 10, overflow: "hidden" }}>
                                        <Paper radius={0} style={{ borderTopLeftRadius: 10, borderTopRightRadius: 10 }} bg="#f9f9f9" p="sm" mt={10} className='flex aic jcsb'>
                                            <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Name</Text>
                                            <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Quantity</Text>
                                            <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Tax</Text>
                                            {/* <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Tax Status</Text> */}
                                            <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Unit Name</Text>
                                            <Text style={{ flex: 1 }} fz="sm" fw={600} c="dimmed">Unit Price</Text>
                                            <Text fz="sm" fw={600} c="dimmed">Total</Text>
                                        </Paper>
                                        {inv.items.map((item, i) => {
                                            console.log(bexioData.taxes, item.tax)

                                            return (
                                                <>
                                                    {/* {i !== 0 */}
                                                    <Divider key={i + "_divider"} opacity={.5} />
                                                    {/* : null */}
                                                    {/* } */}
                                                    <Paper key={i} radius={0} bg="#f9f9f9" p="sm" className='flex aic jcsb'>
                                                        <Text fz="sm" style={{ flex: 1 }} fw={500}>{item.name}</Text>
                                                        <Text fz="sm" style={{ flex: 1 }} fw={500}>{item.unitQ}</Text>
                                                        <Tooltip position="bottom-start" withArrow label={bexioData.taxes?.find((t) => t?.id === parseInt(item.tax as unknown as string))?.display_name}>
                                                            <Text fz="sm" style={{ flex: 1 }} fw={500}>{bexioData.taxes?.find((t) => t?.id === parseInt(item.tax as unknown as string))?.value}%</Text>
                                                        </Tooltip>
                                                        {/* <Text style={{ flex: 1 }} fz="sm" fw={500}>{!inv.taxStatus ? "Inclusive" : (inv.taxStatus === 1 ? "Exclusive" : "Exempt")}</Text> */}

                                                        <Text fz="sm" style={{ flex: 1 }} fw={500}>{bexioData.units?.find((t) => t?.id === parseInt(item.unitID as unknown as string))?.name}</Text>

                                                        <Text fz="sm" style={{ flex: 1 }} fw={500}>{item.unitPrice}</Text>
                                                        <Text fz="sm" fw={500}>{item.unitPrice * item.unitQ}</Text>
                                                    </Paper>
                                                </>

                                            )
                                        })}
                                        <Divider />
                                        <Box p="sm" className='flex aic jcsb' bg="#f9f9f9">
                                            <Text fz="sm" fw={500}>Total:</Text>
                                            <Text fz="sm" fw={600}>{inv.items.reduce((prev, current) => (prev + (current.unitPrice * current.unitQ)), 0)}</Text>
                                        </Box>
                                    </div>
                                    <div className='flex aic jcsb' style={{ marginTop: 18 }}>
                                        <div className='flex' style={{ gap: 10 }}>
                                            {/* <Percentage size={20} /> */}
                                            <Box>
                                                <Text lh={1} fw={600} fz="sm">Tax Status</Text>
                                                <Text mt={3} lh={1} fz="sm" c="dimmed">This is applied to all line items</Text>
                                            </Box>
                                        </div>
                                        <Badge color={!inv.taxStatus ? "green" : (inv.taxStatus === 1 ? "blue" : "gray")} variant="light">
                                            {!inv.taxStatus ? "Inclusive" : (inv.taxStatus === 1 ? "Exclusive" : "Exempt")}
                                        </Badge>
                                        {/* <Link href={`/dashboard/invoices/${inv.id}/edit`}>
                                            <Button disabled={!!inv.DELETED} leftSection={<Edit size={20} />} variant="subtle" size={"xs"}>Edit</Button>
                                        </Link> */}
                                    </div>
                                </div>

                                <div style={{ marginTop: 30, paddingBottom: 30 }}>
                                    <div className='flex aic' style={{ gap: 10 }}>
                                        <User size={20} />
                                        <Text fw={600}>Customer</Text>
                                    </div>
                                    <Divider variant={"dashed"} my={15} />
                                    <div>
                                        {contact
                                            ? <Box className='flex' style={{ gap: 15 }}>
                                                {contact.profile_image
                                                    ? <Image style={{ borderRadius: 10 }} alt='Customer profile picture' width={45} height={45} src={"data:image/png;base64," + contact.profile_image} />
                                                    : <Avatar size={45} />
                                                }
                                                <div>
                                                    <Text lineClamp={1} fw={500}>{contact.name_1} {contact.name_2}</Text>
                                                    <Text style={{ position: "relative", bottom: 6 }} fz={"sm"} c="dimmed">{contact.mail}{contact.phone_fixed ? <><Text fz={"xl"} px={8} span>{"•"}</Text>{contact.phone_fixed}</> : ""}</Text>
                                                </div>
                                            </Box>
                                            : <div className='flex aic jcc' style={{ padding: "20px 0" }}>
                                                <Loader size={"sm"} />
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Box >
            </div >
        </>
    )
}
