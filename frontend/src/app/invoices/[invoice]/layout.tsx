"use client"
import Error from '@/components/global/Error'
import { RecurringInvoicesContext } from '@/context/RecurringInvoices.context'
import api from '@/utils/api/api'
import { Loader } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import React, { createContext, PropsWithChildren, useContext, useEffect, useState } from 'react'
import { BexioContact, RecurringInvoice } from '../../../../../types/global'
import { InvoicePageContext } from '@/context/InvoicePage.context'


export default function Layout({ children, params }: PropsWithChildren & { params: { invoice: string } }) {
    const [contact, setContact] = useState<BexioContact | undefined>(undefined)
    // useEffect(() => {
    //     setInvoice(invoices.find((i) => i.id === iid) || null)
    // }, [])

    const [inv, setInv] = useState<null | undefined | RecurringInvoice>(undefined)
    const invoices = useContext(RecurringInvoicesContext)
    useEffect(() => {
        setInv(invoices.find((i) => i.id === params.invoice) || null)
    }, [invoices])
    useEffect(() => {
        if (!inv) {
            return
        }
        api("/bexio/get-contact", {
            id: inv.customerID
        }).then(res => {
            if (res.error) {
                return notifications.show({
                    color: "red",
                    title: "Error",
                    message: res.msg
                })
            }
            setContact(res.data)
        })
    }, [inv])

    if (inv === undefined || !contact) {
        return <div className='flex aic jcc'>
            <Loader size={"sm"} />
        </div>
    }
    if (inv === null) {
        return (
            <Error>We couldn't find that invoice</Error>
        )
    }
    console.log(children)
    return (
        <>
            <InvoicePageContext.Provider value={{
                invoice: inv,
                contact: contact
            }}>
                <div>{children}</div>
            </InvoicePageContext.Provider>
        </>
    )
}
