"use client";
import React, { useEffect } from 'react'
import { Box, Loader } from '@mantine/core'
import { signOut } from 'firebase/auth'
import { auth } from '@/utils/firebase/firebase'
import { useRouter } from 'next/navigation';

export default function Logout() {
    const router = useRouter()
    useEffect(() => {
        signOut(auth).then(() => {
            router.push("/")
        })
    }, [])
    return (
        <Box className='flex aic jcc fdc'>
            <Loader size={"sm"} />
        </Box>
    )
}
