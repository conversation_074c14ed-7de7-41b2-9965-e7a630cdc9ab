"use client"
import CreateInvoiceModal, { DraftInvoiceModal } from '@/components/dashboard/InvoiceModals'
import { DraftsContext } from '@/context/Drafts.context'
import useDrafts from '@/hooks/useDrafts'
import { firestore } from '@/utils/firebase/firebase'
import { ActionIcon, Box, Button, Divider, Paper, Text } from '@mantine/core'
import { useHover } from '@mantine/hooks'
import { deleteDoc, doc } from 'firebase/firestore'
import React, { useContext, useState } from 'react'
import { Eye, FileOff, Plus, Trash } from 'tabler-icons-react'
import { DBInvoiceDraft } from '../../../../types/global'

const Draft = ({ draft }: { draft: DBInvoiceDraft & { id: string } }) => {
    const { hovered, ref } = useHover()
    const [loading, setLoading] = useState(false)
    return (
        <>
            <Box bg={hovered ? "gray.1" : "gray.0"} className='flex aic jcsb' ref={ref}>
                <DraftInvoiceModal draft={draft.draft} draftID={draft.id} target={<Box p="md" className='cp' style={{ flex: 1 }}>
                    <Text fz="sm" fw={500}>{draft.draft.title || draft.draft.rebill_description || `Created ${new Date(draft.created).toDateString()}`}</Text>
                    <Text c="dimmed" fz="xs">#{draft.id}</Text>
                </Box>} />
                <Box p="md">
                    {hovered
                        ? <Box className='flex aic' style={{ gap: 10 }}>
                            <ActionIcon loading={loading} variant={"subtle"} onClick={async () => {
                                setLoading(true)
                                await deleteDoc(doc(firestore, "drafts", draft.id))
                                setLoading(false)
                            }}>
                                <Trash size={20} color='var(--mantine-color-dimmed)' />
                            </ActionIcon>
                        </Box>
                        : null
                    }

                </Box>
            </Box>
        </>
    )
}

export default function Page() {
    const drafts = useContext(DraftsContext)
    return (
        <>
            <title>Drafts | reBill</title>
            <div>
                <div className='flex aic jcsb'>
                    <div>
                        <Text fw={800} fz={40} style={{ lineHeight: 1 }}>
                            <Text fw={800} fz={20} c="dimmed">
                                Invoice
                            </Text>
                            Drafts
                        </Text>
                    </div>
                    <CreateInvoiceModal target={<Button leftSection={<Plus size={20} />}>Create New</Button>} />
                </div>
                <Paper bg="gray.0" mt={30} className="flex fdc" radius={"lg"} style={{ gap: 0, overflow: "hidden" }}>
                    {drafts?.sort((a, b) => b.created - a.created).map((draft, i) => (
                        <>
                            {i !== 0
                                ? <Divider w={"100%"} opacity={.5} />
                                : null
                            }
                            <Draft draft={draft} />
                        </>
                    ))}
                    {!drafts.length
                        ? <div className='flex aic jcc fdc' style={{ gap: 10, padding: "50px 0" }}>
                            <FileOff color='var(--mantine-color-dimmed)' />
                            <Text fw={500} c="dimmed">You don't have any drafts yet</Text>
                        </div>
                        : null
                    }
                </Paper>
            </div>
        </>
    )
}
