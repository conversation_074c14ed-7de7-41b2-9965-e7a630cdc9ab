"use client"
import DashboardLayoutController from '@/components/dashboard/DashboardLayoutController'
import AuthGuard from '@/components/global/AuthGuard'
import BexioDataProvider from '@/providers/BexioDataProvider'
import DraftsProvider from '@/providers/DraftsProvider'
import InvoicesProvider from '@/providers/InvoicesProvider'
import RecurringInvoiceProvider from '@/providers/RecurringInvoiceProvider'
import React, { PropsWithChildren, useEffect, useState } from 'react'

export default function DashboardLayout({ children }: PropsWithChildren) {
    return (
        <AuthGuard>
            <RecurringInvoiceProvider>
                <DraftsProvider>
                    <InvoicesProvider>
                        <BexioDataProvider>
                            <DashboardLayoutController>
                                {children}
                            </DashboardLayoutController>
                        </BexioDataProvider>
                    </InvoicesProvider>
                </DraftsProvider>
            </RecurringInvoiceProvider>
        </AuthGuard>
    )
}
