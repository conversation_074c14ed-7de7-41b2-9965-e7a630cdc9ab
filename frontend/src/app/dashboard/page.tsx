"use client"
import CreateInvoiceModal, { DraftInvoiceModal } from '@/components/dashboard/InvoiceModals'
import { Stat } from '@/components/dashboard/Stat'
import { DraftsContext } from '@/context/Drafts.context'
import { InvoicesContext } from '@/context/Invoices.context'
import { RecurringInvoicesContext } from '@/context/RecurringInvoices.context'
import useDrafts from '@/hooks/useDrafts'
import useRecurringInvoices from '@/hooks/useRecurringInvoices'
import { UserContext } from '@/utils/context/User.context'
import { firestore } from '@/utils/firebase/firebase'
import { ActionIcon, Badge, Box, Button, Divider, Paper, Text } from '@mantine/core'
import { useHover } from '@mantine/hooks'
import { deleteDoc, doc } from 'firebase/firestore'
import Link from 'next/link'
import React, { useContext, useEffect, useState } from 'react'
import { Plus, Receipt, Trash } from 'tabler-icons-react'
import { DBInvoiceDraft, RecurringInvoice } from '../../../../types/global'

const ListContainer = ({ title, action, children, href }: { title: string, action?: React.ReactElement, children?: React.ReactElement, href?: string }) => {
    return (
        <Paper bg={"#fff"} withBorder maw={"50%"} radius={10} style={{ flex: 1, height: "fit-content" }}>
            <div className='flex aic jcsb' style={{ padding: "15px 20px" }}>
                <Text fw={600} fz="lg">{title}</Text>
                {action}
            </div>
            <Divider variant={"dashed"} />
            <div style={{ overflow: "hidden", borderRadius: 10 }}>
                {children}
            </div>
            {href
                ? <div className='flex aic jcc' style={{ position: "relative", width: "100%" }}>
                    <Link href={href} style={{ position: "absolute", bottom: -15 }}>
                        <Button variant={"default"} size="xs">View All</Button>
                    </Link>
                </div>
                : null
            }

        </Paper>
    )
}

const Invoices = () => {
    const recurringInvoices = useContext(RecurringInvoicesContext)

    const Invoice = ({ invoice, i }: { invoice: RecurringInvoice, i: number }) => {
        const { hovered, ref } = useHover()
        return (
            <Link href={`/invoices/${invoice.id}`}>
                <Box ref={ref} p={15} bg={hovered ? "#f9f9f9" : "#fff"} style={{ borderTop: `${!i ? "0" : "1"}px solid rgba(0,0,0,.1)` }} className="flex jcsb">
                    <div style={{ gap: 10 }} className="flex cp">
                        <Paper className='flex aic jcc fdc' p={5} bg="dark.6" style={{ aspectRatio: '1 / 1', lineHeight: .7, width: 45, height: 45 }} color={"gray"}>
                            <Text lh={1.1} fw={700} c="#fff" fz="sm" >{new Date(invoice.nextCharge).getDate()}</Text>
                            <Text lh={1.1} fz={"xs"} c="#fff" style={{ textTransform: "uppercase" }} fw={700}>{new Date(invoice.nextCharge).toLocaleString('en-us', { month: 'short' })}</Text>
                        </Paper>
                        <div>
                            <Text fw={500} fz={"sm"}>{invoice.rebill_title}</Text>
                            <Text fz={"xs"} c="dimmed">{invoice.rebill_description}</Text>

                            <div className='flex aic' style={{ gap: 10, marginTop: 4 }}>
                                {invoice.DELETED
                                    ? <Badge size={"sm"} color={"red"} variant="light">Deleted</Badge>
                                    : <Badge size={"sm"} color={invoice.status === "active" ? "green" : "gray"} variant="light">{invoice.status === "active" ? "Active" : "Inactive"}</Badge>
                                }
                                <Badge variant={"light"} color="gray" size={"sm"}>{invoice.period}</Badge>
                            </div>
                        </div>
                    </div>
                </Box>
            </Link >
        )
    }

    return (
        <ListContainer href='/invoices' title='Upcoming Recurring Invoices' action={<CreateInvoiceModal target={<Button size={'xs'} leftSection={<Plus size={20} />}>Create New</Button>} />} children={<div>
            {!recurringInvoices?.filter((i) => i.status === "active").length
                ? <Box className='flex aic fdc' w={"100%"} py={40}>
                    <Receipt size={24} color={"var(--mantine-color-dimmed)"} />
                    <Text fw={500} c="dimmed">You don't have any invoices</Text>
                </Box>
                : <Box w={"100%"} className='flex fdc'>
                    {recurringInvoices.filter((i) => i.status === "active").sort((a, b) => a.nextCharge - b.nextCharge).splice(0, 10).map((inv, i) => {
                        return <Invoice invoice={inv} i={i} />
                    })}
                </Box>
            }
        </div>} />
    )
}

const Drafts = () => {
    const drafts = useContext(DraftsContext)

    const Draft = ({ draft, onClick, i, remove }: { draft: DBInvoiceDraft & { id: string }, onClick?: any, i: number, remove: (id: string) => void }) => {
        const { hovered, ref } = useHover()
        const [loading, setLoading] = useState(false)
        return (
            <Box ref={ref} p={15} bg={hovered ? "#f9f9f9" : "#fff"} style={{ borderTop: `${!i ? "0" : "1"}px solid rgba(0,0,0,.1)` }} className="flex aic jcsb">
                <div onClick={onClick} style={{ gap: 10, flex: 1 }} className="flex aic cp">
                    <Receipt size={20} />
                    <div>
                        <Text fz="sm" fw={500}>{draft.draft.rebill_title || draft.draft.rebill_description || `Created ${new Date(draft.created).toDateString()}`} {draft.draft.customerDetails?.name ? `- ${draft.draft.customerDetails?.name}` : ""}</Text>
                        <Text c="dimmed" fz="xs">#{draft.id}</Text>
                    </div>
                </div>
                <ActionIcon color={"gray"} variant="subtle" loading={loading} onClick={() => {
                    setLoading(true)
                    remove(draft.id)
                }}><Trash size={20} /></ActionIcon>
            </Box>
        )
    }

    return (
        <ListContainer href='/drafts' title='Drafts' children={<div>
            {!drafts?.length
                ? <Box className='flex aic fdc' w={"100%"} py={40}>
                    <Receipt size={25} color={"var(--mantine-color-dimmed)"} />
                    <Text fw={600} c="dimmed">You don't have any drafts</Text>
                </Box>
                : <Box w={"100%"} className='flex fdc'>
                    {drafts.map((d, i) => {
                        return <DraftInvoiceModal target={<Draft draft={d} i={i} remove={() => {
                            deleteDoc(doc(firestore, "drafts", d.id))
                        }} />} draft={d.draft} draftID={d.id} />
                    })}
                </Box>
            }
        </div>} />
    )
}

export default function Page() {
    const user = useContext(UserContext)
    const invoices = useContext(InvoicesContext)
    const recurringInvoices = useContext(RecurringInvoicesContext)

    return (
        <>
            <title>Dashboard | reBill</title>
            <div>
                <Paper className='flex aic' style={{ gap: 20, marginTop: 0, }}>
                    <div style={{ flex: 1 }}>
                        <Text fw={800} fz={40} style={{ lineHeight: 1 }}>
                            <Text fw={800} fz={20} c="dimmed">
                                Welcome back, {user.name}
                            </Text>
                            Dashboard
                        </Text>
                    </div>
                    <div style={{ flex: 1 }}>

                        <div className='flex aic' style={{ gap: 10 }}>
                            <Stat label={"Unpaid Invoices"} value={[...invoices].filter((i) => parseInt(i.total_remaining_payments) > 0).length} />
                            <Stat label={"Recurring Invoices"} value={recurringInvoices.length} />
                        </div>
                    </div>
                </Paper>
                <div style={{ marginTop: 30, gap: 20, overflow: "visible" }} className='flex'>
                    <Invoices />
                    <Drafts />
                </div>
            </div>
        </>
    )
}
