"use client"
import Error from '@/components/global/Error'
import Logo from '@/components/global/Logo'
import api from '@/utils/api/api'
import { auth, firestore } from '@/utils/firebase/firebase'
import { ActionIcon, Box, Button, Collapse, Divider, Loader, Paper, PasswordInput, Skeleton, Text, TextInput, ThemeIcon, Transition } from '@mantine/core'
import { doc, updateDoc } from 'firebase/firestore'
import Image from 'next/image'
import Link from 'next/link'
import React, { useContext, useEffect, useState } from 'react'
import { ArrowLeft, ArrowRight, BuildingStore, Check, Key, Tex, X } from 'tabler-icons-react'
import { BexioCompanyProfile, User } from '../../../../types/global'
import page from './translations.json'
import { LangContext } from '@/context/Lang.context'
import { UserContext } from '@/utils/context/User.context'

type Stage = "company" | "api_key" | "complete"
type Details = {
    company?: string,
    apiKey?: string
}
type DetialsInputProps = { setStage: (newStage: Stage) => void }

const CompanyDetialsForm = ({ details, updateDetails }: { details: BexioCompanyProfile, updateDetails: (newDetails: Partial<BexioCompanyProfile>) => void }) => {
    const [name, setName] = useState(details.name || "")

    useEffect(() => {
        updateDetails({ name })
    }, [name])

    return (
        <div className='flex fdc' style={{ gap: 10, marginTop: 20 }}>
            <div>
                <TextInput size={"md"} placeholder='Company Name' value={name} onChange={(e) => setName(e.target.value)} />
            </div>
            <div>
                <TextInput size={"md"} placeholder='Address' value={details.address} disabled />
                <div className='flex aic' style={{ gap: 10, marginTop: 10 }}>
                    <TextInput size={"md"} style={{ flex: 1 }} placeholder='City' value={details.city} disabled />
                    <TextInput size={"md"} style={{ flex: 1 }} placeholder='Postcode' value={details.postcode} disabled />
                </div>
            </div>
        </div>
    )
}

const CompanyInput = ({ setStage }: DetialsInputProps) => {
    const lang = useContext(LangContext)
    const [companyDetails, setCompanyDetails] = useState<BexioCompanyProfile | undefined>(undefined)
    const [error, setError] = useState("")
    const [loading, setLoading] = useState(false)

    // @ts-ignore
    const updateCompanyDetials = (newDetials: Partial<BexioCompanyProfile>) => setCompanyDetails((prev) => ({ ...prev, ...newDetials }))

    useEffect(() => {
        api("/bexio/get-business").then((res) => {
            if (res.error) {
                return setError(res.msg)
            }
            console.log(res.data)
            setCompanyDetails(res.data)
        })
        // updateDetails({ company })
    }, [])
    return (
        <Box w={"100% "} maw={500}>
            {!error
                ? <>
                    {companyDetails
                        ? <>
                            {companyDetails.has_own_logo
                                ? <Image alt='Company logo' style={{ width: 50, height: 50, objectFit: "contain", borderRadius: 20 }} width={40} height={40} src={"data:image/png;base64," + companyDetails.logo_base64} />
                                : <BuildingStore size={40} />
                            }
                        </>
                        : <Loader size={"md"} type="dots" />
                    }
                </>
                : <ThemeIcon color={"red"} variant="light"><X /></ThemeIcon>
            }

            {!error
                ? <>
                    <Text fw={600} fz={30}>{companyDetails
                        // @ts-ignore
                        ? page.steps[1].title.success[lang]
                        // @ts-ignore
                        : page.steps[1].title?.loading[lang]
                    }</Text>
                    <Text fz={"lg"} c="dimmed">{companyDetails ? page.steps[1].subtitle?.success[lang] : page.steps[1].subtitle?.loading[lang]}</Text>
                </>
                : <>
                    {/* @ts-ignore */}
                    <Text fw={600} fz={30}>{page.steps[1].title.error[lang]}</Text>
                    {/*@ts-ignore */}
                    <Text fz={"lg"} c="dimmed">{page.steps[1].subtitle.error[lang]}</Text>
                </>
            }


            {!error
                ? <>
                    {!companyDetails
                        ? <div className='flex fdc' style={{ gap: 10, marginTop: 20 }}>
                            <Skeleton w={"100%"} h={80} />
                            <Skeleton w={"100%"} h={80} />
                            <Skeleton w={"100%"} h={80} />
                        </div>
                        : <CompanyDetialsForm updateDetails={updateCompanyDetials} details={companyDetails} />
                    }
                </>
                : null
            }

            {/* <TextInput value={company} fw={500} onChange={(e) => setCompany(e.target.value)} size={"xl"} styles={{ input: { borderLeft: "none", borderRight: "none", borderTop: "none", borderRadius: 0, borderWidth: 1, paddingLeft: 0 } }} placeholder="Company Name" />
            
            <Collapse in={!!company.length}>
                <Button size={"md"} variant="default" rightSection={<ArrowRight size={20} />} mt={15} onClick={() => setStage("api_key")}>Continue</Button>
            </Collapse> */}
            <div className='flex aic' style={{ gap: 5, marginTop: 20 }}>
                <Button size={"md"} disabled={loading} variant="default" onClick={() => setStage("api_key")}>{page.steps[1].buttons?.back[lang]}</Button>
                <Button loading={loading} disabled={!!error || !companyDetails || !companyDetails.name} size={"md"} rightSection={<ArrowRight size={20} />} onClick={() => {
                    setLoading(true)
                    updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                        company: {
                            name: companyDetails?.name as string,
                            id: companyDetails?.id.toString() as string,
                            pfp: companyDetails?.logo_base64 ? "data:image/png;base64," + companyDetails.logo_base64 : ""
                        }
                    } as Partial<User>).then(() => {
                        setLoading(false)
                        setStage("complete")
                    })

                }}>{page.steps[1].buttons?.continue[lang]}</Button>
            </div>
        </Box>
    )
}

const APIKeyInput = () => {
    return (
        <Box w={"100% "} maw={500}>
            <Key size={40} />
            <Text fw={600} fz={30}>Login with Bexio</Text>
            <Text c="dimmed">In order to send invoices on your behalf, you'll need to login with Bexio, and grant reBill the required permissions</Text>
            <Button leftSection={<Image alt='Bexio logo' src={"/logo/bexio.png"} width={22} height={22} />} mt={10} size='md' color='green' component={Link} href={`https://auth.bexio.com/realms/bexio/protocol/openid-connect/auth?client_id=1f4ac48c-53c7-4bee-96ca-eac904753097&redirect_uri=${process.env.NODE_ENV === "production" ? "https://app.rebill.ch" : "http://localhost:3000"}/oauth/bexio&response_type=code&scope=${"kb_invoice_show kb_invoice_edit contact_show contact_edit project_show offline_access"}`}>Continue to Bexio</Button>
            <Link href={"https://docs.bexio.com/#section/Authentication/OpenID-Connect"} target="_blank">
                {/* @ts-ignore */}
                <Text mt={10} className="cp" c="dimmed" style={{ textDecoration: "underline" }}>Learn More</Text>
            </Link>
        </Box>
    )
}

const TransitionContainer = ({ mounted, children }: { mounted: boolean, children: React.ReactElement }) => {
    const [isIn, setIsIn] = useState(false)
    useEffect(() => {
        if (!mounted) {
            setIsIn(mounted)
        } else {
            setTimeout(() => {
                setIsIn(mounted)
            }, 500)
        }
    }, [mounted])
    return (
        <Transition transition={"pop"} mounted={isIn} duration={300} exitDuration={300}>
            {(styles) => (
                <div style={{ ...styles, width: '100%' }} className="flex aic jcc">
                    {children}
                    {/* <CompanyInput updateDetails={updateDetails} setStage={setStage} /> */}
                </div>
            )}
        </Transition>
    )
}

const AccountSetup = () => {
    return (
        <Paper p="sm" withBorder className='flex aic jcc fdc' w={"100%"} maw={400} py="lg">
            <ThemeIcon variant={"light"} color={"green"} ><Check size={18} /></ThemeIcon>

            <Text fw={600} mt={10} fz='lg'>Setup Complete</Text>
            <Text fz={"sm"} c="dimmed">Welcome to reBill. Head to the dashboard to get started</Text>
            <Link href={"/"}><Button mt={15} variant="default" rightSection={<ArrowRight size={20} />}>Go to Dashboard</Button></Link>
        </Paper>
    )
}

export default function Setup() {
    const user = useContext(UserContext)
    const [stage, setStage] = useState<Stage>(user.bexioLinked ? "company" : "api_key")

    useEffect(() => {
        if (user.bexioLinked && stage === "api_key") {
            setStage('company')
        }
    }, [user])

    return (
        <div style={{ width: "100vw", height: "100vh" }} className="flex">
            <Box className='flex aic jcc' style={{ flex: 1, position: "relative" }} bg="dark.6">
                <div style={{ position: "absolute", top: 20, left: 20, gap: 10 }} className="flex">
                    <div style={{ width: "100%" }}>
                        <Link href={"/"}>
                            <ActionIcon size={"lg"}>
                                <ArrowLeft size={20} />
                            </ActionIcon>
                        </Link>
                    </div>
                    <div style={{ position: "relative", bottom: 10 }}>
                        <Logo size={35} light />
                    </div>
                </div>
            </Box>
            <Box style={{ flex: 3 }} className="flex aic jcc">
                <TransitionContainer mounted={stage === "api_key"}>
                    <APIKeyInput />
                </TransitionContainer>
                <TransitionContainer mounted={stage === "company"}>
                    <CompanyInput setStage={setStage} />
                </TransitionContainer>
                <TransitionContainer mounted={stage === "complete"}>
                    <AccountSetup />
                </TransitionContainer>
            </Box>
        </div>
    )
}