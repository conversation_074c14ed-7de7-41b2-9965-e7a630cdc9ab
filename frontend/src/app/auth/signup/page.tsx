"use client"
import Error from '@/components/global/Error'
import { Button, Divider, PasswordInput, Text, TextInput } from '@mantine/core'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { createUserWithEmailAndPassword, AuthErrorCodes } from 'firebase/auth'
import { doc, onSnapshot, updateDoc } from 'firebase/firestore'
import React, { useContext, useState } from 'react'
import { auth, firestore } from '@/utils/firebase/firebase'
import { User } from '../../../../../types/global'
import { authenticateWithGoogle } from '@/utils/auth/google'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import page from './translations.json'
import { LangContext } from '@/context/Lang.context'

const listenForSignupFulfillment = (name: string, router: AppRouterInstance) => {
    const unsub = onSnapshot(doc(firestore, "users", auth.currentUser?.uid as string), async (snap) => {
        if (snap.exists()) {
            unsub()
            await updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                name,
            } as Partial<User>)
            router.push("/setup")
        }
    })
}

export default function Signup() {
    const [name, setName] = useState("")
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")

    const [loading, setLoading] = useState(false)
    const [error, setError] = useState("")
    const lang = useContext(LangContext)
    const router = useRouter()
    return (
        <>
            <Text fw={800} fz={30}>{page.title[lang]}</Text>
            <Text c={"dimmed"}>{page.altPrompt[lang]} <Text span fw={600} className="cp" onClick={() => router.push("/auth/login")} c={"#000"}>{page.altAction[lang]}</Text></Text>
            <div style={{ marginTop: 25, gap: 5, width: "100%", maxWidth: 350 }} className="flex fdc">
                <TextInput value={name} onChange={(e) => setName(e.target.value)} placeholder={page.form.name[lang]} size={"md"} />
                <TextInput value={email} onChange={(e) => setEmail(e.target.value)} placeholder={page.form.email[lang]} size={"md"} />
                <PasswordInput value={password} onChange={(e) => setPassword(e.target.value)} placeholder={page.form.password[lang]} size={"md"} />
                <Button size={"md"} mt={10} loading={loading} disabled={!name || !email || !password} onClick={() => {
                    setLoading(true)
                    createUserWithEmailAndPassword(auth, email, password).then(async () => {
                        await auth.currentUser?.reload()
                        listenForSignupFulfillment(name, router)
                    }).catch(err => {
                        setLoading(false)
                        const code = err.code
                        switch (code) {
                            case AuthErrorCodes.EMAIL_EXISTS:
                                setError("That email is already in use")
                                break
                            case AuthErrorCodes.WEAK_PASSWORD:
                                setError("That password is too weak")
                                break
                            case AuthErrorCodes.INVALID_EMAIL:
                                setError("That email is invalid")
                                break
                            default:
                                setError("Something went wrong. Please try again.")
                        }
                    })
                }}>{page.form.button[lang]}</Button>
                <Error mt={5}>{error}</Error>
            </div>
        </>
    )
}
