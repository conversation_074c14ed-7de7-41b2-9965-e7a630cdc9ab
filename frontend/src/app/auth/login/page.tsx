"use client"
import Error from '@/components/global/Error'
import { auth } from '@/utils/firebase/firebase'
import { Alert, Box, Button, PasswordInput, Text, TextInput, em } from '@mantine/core'
import { AuthErrorCodes, sendPasswordResetEmail, signInWithEmailAndPassword } from 'firebase/auth'
import { useRouter } from 'next/navigation'
import React, { useContext, useEffect, useState } from 'react'
// import page from './translations.json'
import { LangContext } from '@/context/Lang.context'
import { ArrowRight } from 'tabler-icons-react'
import { getHotkeyHandler } from '@mantine/hooks'

export default function Login() {
    const page = {
        "title": {
            "en": "Login",
            "de": "Anmeldung"
        },
        "altPrompt": {
            "en": "Don't have an account?",
            "de": "Sie haben noch kein Konto?"
        },
        "altAction": {
            "en": "Sign Up",
            "de": "<PERSON>den Sie sich an"
        },
        "form": {
            "email": {
                "en": "Email",
                "de": "Email"
            },
            "password": {
                "en": "Password",
                "de": "Passwort"
            },
            "button": {
                "en": "Login",
                "de": "Anmeldung"
            }
        }
    }
    const lang = useContext(LangContext)
    const [mode, setMode] = useState<"auth" | "forgot_password">("auth")
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState("")
    const [passwordResetSent, setPasswordResetSent] = useState(false)
    const router = useRouter()

    const signIn = () => {
        setError("")
        setLoading(true)
        signInWithEmailAndPassword(auth, email, password).then(() => router.push("/")).catch(err => {
            const code = err.code
            console.log(code)
            setError("Email or password incorrect")
        }).finally(() => setLoading(false))
    }

    useEffect(() => {
        if (!passwordResetSent) return;
        setTimeout(() => {
            setPasswordResetSent(false)
        }, 5000);
    }, [passwordResetSent])

    if (mode === "forgot_password") {
        return (
            <>
                <Text fw={800} fz={30}>Reset Password</Text>
                <Text ta={"center"} c={"dimmed"}>Enter your email address and we'll send you a password reset email</Text>
                <Box mt={5} w="100%">

                    <TextInput placeholder='Email Address' value={email} onChange={(e) => setEmail(e.target.value)} />
                    <Button loading={loading} disabled={!email} mt={6} rightSection={<ArrowRight size={18} />} fullWidth onClick={() => {
                        setLoading(true)
                        sendPasswordResetEmail(auth, email).then(() => {
                            setLoading(false)
                            setPasswordResetSent(true)
                            setMode("auth")
                        }).catch(err => {
                            setLoading(false)
                            switch (err.code) {
                                case AuthErrorCodes.INVALID_EMAIL:
                                    setError("That email address is invalid")
                                    break;
                                default:
                                    setError("Something went wrong. Please try again.")
                                    break;
                            }

                        })
                    }}>Reset Password</Button>
                    <Button variant="default" fullWidth mt={5} onClick={() => setMode("auth")}>Back</Button>
                </Box>
            </>
        )
    }

    return (
        <>
            <Text fw={800} fz={30}>{page.title[lang]}</Text>
            <Text c={"dimmed"}>{page.altPrompt[lang]} <Text span fw={600} className="cp" onClick={() => router.push("/auth/signup")} c={"#000"}>{page.altAction[lang]}</Text></Text>
            <div style={{ marginTop: 25, gap: 5, width: "100%", maxWidth: 350 }} className="flex fdc">
                <TextInput value={email} onChange={(e) => setEmail(e.target.value)} placeholder={page.form.email[lang]} size={"md"} />
                <PasswordInput value={password} onChange={(e) => setPassword(e.target.value)} placeholder={page.form.password[lang]} size={"md"} onKeyDown={getHotkeyHandler([
                    ["Enter", signIn]
                ])} />
                <Button size={"md"} mt={10} loading={loading} disabled={false} onClick={() => {
                    signIn()
                }}>{page.form.button[lang]}</Button>
                <Error mt={5}>{error}</Error>
                <Button variant='subtle' size="compact-sm" w={"fit-content"} onClick={() => setMode("forgot_password")}>I've forgotten my password</Button>
                {passwordResetSent
                    ? <Alert color="green" title="Success">Password reset email sent</Alert>
                    : null
                }
            </div>
        </>
    )
}
