"use client"
import { DefaultHeader } from '@/components/global/Headers'
import { Paper, Transition } from '@mantine/core'
import React, { PropsWithChildren, useEffect, useState } from 'react'

export default function Layout({ children }: PropsWithChildren) {
    const [mounted, setMounted] = useState(false)
    // useEffect(() => {
    //     setMounted(true)
    // }, [])
    return (
        <>
            <DefaultHeader />
            <div style={{ width: "100vw", height: "100vh" }} className="flex aic jcc background-grid">
                <div style={{ width: "100%" }} className="flex aic jcc">
                    <Paper style={{ border: "3px solid black" }} shadow={"lg"} maw={450} py={40} w="100%" p={"md"} className="flex aic jcc fdc">
                        {children}
                    </Paper>
                </div>
            </div>
        </>
    )
}
