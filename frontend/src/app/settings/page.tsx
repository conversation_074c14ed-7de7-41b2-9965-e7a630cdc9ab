"use client"
import { CredentialsModal } from '@/components/auth/CredentialModal'
import Error from '@/components/global/Error'
import api from '@/utils/api/api'
import { UserContext } from '@/utils/context/User.context'
import { getEmailTemplate } from '@/utils/data/getTemplate'
import { auth, firestore } from '@/utils/firebase/firebase'
import { Alert, Box, Button, Collapse, Divider, Image, Modal, Paper, PasswordInput, Select, Tabs, Text, Textarea, TextInput } from '@mantine/core'
import { Notifications } from '@mantine/notifications'
import { AuthErrorCodes, EmailAuthProvider, reauthenticateWithCredential, signOut, updateEmail, updatePassword, User } from 'firebase/auth'
import { doc, updateDoc } from 'firebase/firestore'
import { useRouter } from 'next/navigation'
import React, { useContext, useEffect, useState } from 'react'
import { ArrowRight, Currency, FileInvoice, Key, Logout, Settings, UserCircle } from 'tabler-icons-react'
import { BexioCurrency, BexioTax, Language, User as UserDoc } from '../../../../types/global'

const defaultCredentialModalValue = {
    open: false,
    onSubmit: () => void 0,
    onCancel: () => void 0
}


const GroupHeader = ({ title, icon }: { title: string, icon: React.ReactElement }) => {
    return (
        <div style={{ marginTop: 30 }}>
            <div className='flex aic' style={{ gap: 10 }}>
                {React.cloneElement(icon, { size: 20 })}
                <Text fw={600}>{title}</Text>
            </div>
            <Divider my={15} />
        </div>
    )
}

const UserSettings = () => {
    const user = useContext(UserContext)
    const [name, setName] = useState(user.name || "")
    const [company, setCompany] = useState(user.company?.name || "")
    const [email, setEmail] = useState(user.email || "")
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState("")

    const [credentialModal, setCredentialModal] = useState<{
        open: boolean,
        onSubmit: () => any,
        onCancel: () => any
    }>({ ...defaultCredentialModalValue })

    useEffect(() => {
        if (error) {
            Notifications.show({
                title: "Error",
                message: error,
                color: "red"
            })
        }
        setError("")
    }, [error])

    const attemptUpdateEmail = () => new Promise((res, rej) => {
        updateEmail(auth.currentUser as User, email as string).then(async () => {
            await updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                email
            } as Partial<User>)
            // @ts-ignore
            user?.update({
                email: email as string
            })
            res(undefined)
        }).catch(err => {
            setLoading(false)
            console.log(err)
            const code = err.code
            rej(code)
        })
    })

    const updateValues = async () => {
        setError("")
        setLoading(true)
        if (name !== user?.name || company !== user?.company?.name) {
            await updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                name,
                "company.name": company
            } as Partial<UserDoc>)
            // @ts-ignore
            user?.update({
                name,
                // @ts-ignore
                company: {
                    ...user.company,
                    name: company
                }
            })
        }
        if (email !== user?.email) {
            await attemptUpdateEmail().then(async () => {
                setLoading(false)
                Notifications.show({
                    color: "green",
                    title: "Success",
                    message: "Chanegs saved"
                })
            }).catch((code) => {
                switch (code) {
                    case AuthErrorCodes.EMAIL_EXISTS:
                        setError("That email is already in use")
                        break
                    case AuthErrorCodes.INVALID_EMAIL:
                        setError("That email is invalid")
                        break
                    case AuthErrorCodes.CREDENTIAL_TOO_OLD_LOGIN_AGAIN:
                        setCredentialModal({
                            open: true,
                            onSubmit: () => {
                                console.log("Calling onSubmit")
                                attemptUpdateEmail().then(() => {
                                    setLoading(false)
                                    Notifications.show({
                                        color: "green",
                                        title: "Success",
                                        message: "Chanegs saved"
                                    })
                                })
                            },
                            onCancel: () => {
                                setEmail(user?.email)
                                setLoading(false)
                            }
                        })
                        break
                    default:
                        setError("Something went wrong")
                        break
                }
            })
        } else {
            setLoading(false)
            Notifications.show({
                color: "green",
                title: "Success",
                message: "Chanegs saved"
            })
        }
    }

    return (
        <>
            <Modal
                opened={credentialModal.open}
                onClose={() => setCredentialModal({ ...defaultCredentialModalValue })}
                title={<Text fw={600}>Enter your current password</Text>}
                zIndex={30000}
            >
                <CredentialsModal close={() => setCredentialModal({ ...defaultCredentialModalValue })} onSubmit={credentialModal.onSubmit} onCancel={credentialModal.onCancel} />
            </Modal>
            <div>
                <div className='flex' style={{ gap: 10, maxWidth: 800, flexWrap: "wrap" }}>
                    <TextInput w={"calc(50% - 10px)"} value={name} onChange={(e) => setName(e.target.value)} label="Name" description="Your full name" />
                    <TextInput w={"calc(50% - 10px)"} value={company} onChange={(e) => setCompany(e.target.value)} label="Company" description="Your company's legal name" />
                    <TextInput w={"calc(50% - 10px)"} value={email} onChange={(e) => setEmail(e.target.value)} label="Email address" description="Your company email address" />
                </div>
                <Button
                    loading={loading}
                    mt={20}
                    disabled={name === user.name && company === user.company?.name && email === user.email}
                    onClick={updateValues}
                >Save Changes</Button>
            </div>
        </>
    )
}

const Templates = () => {
    const user = useContext(UserContext)
    const [language, setLanguage] = useState<Language | null>(user.settings?.templates?.email?.language || "EN")

    const [emailSubject, setEmailSubject] = useState(user.settings?.templates?.email?.subject || getEmailTemplate(language || "en").subject)
    const [emailBody, setEmailBody] = useState(user.settings?.templates?.email?.body || getEmailTemplate(language || "en").body)

    const [loading, setLoading] = useState(false)

    useEffect(() => {
        if (!user.settings?.templates?.email?.subject) {
            setEmailSubject(getEmailTemplate(language || "en").subject)
        }
        if (!user.settings?.templates?.email?.body) {
            setEmailBody(getEmailTemplate(language || "en").body)

        }
    }, [language])

    const TemplateString = ({ label, value }: { label: string, value: string }) => {
        return (
            <>
                <Divider />
                <div className='flex aic' style={{ gap: 10 }}>
                    <Text fw={500} fz="sm">{label}</Text>
                    <ArrowRight size={15} />
                    <Text fz="sm">{value}</Text>
                </div>
            </>
        )
    }

    return (
        <div >
            <div style={{ maxWidth: 800 }}>
                {/* @ts-ignore */}
                <Select allowDeselect={false} label="Language" maw={300} value={language} onChange={setLanguage} description="Select a template language" data={["EN", "DE", "FR"] as Array<Language>} />
                <Collapse mt={10} in={!!language}>
                    <TextInput placeholder='RE: Invoice' label="Email Subject" value={emailSubject} onChange={(e) => setEmailSubject(e.target.value)} />
                    <Textarea rows={12} mt={10} label="Email Body" value={emailBody} onChange={(e) => setEmailBody(e.target.value)} />
                </Collapse>
                <Button loading={loading} onClick={async () => {
                    setLoading(true)
                    await updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                        settings: {
                            ...user.settings,
                            templates: {
                                ...user.settings.templates,
                                email: {
                                    subject: emailSubject,
                                    body: emailBody
                                }
                            }
                        }
                    } as Partial<UserDoc>)
                    setLoading(false)
                    Notifications.show({
                        title: "Changes Saved",
                        message: "Your email template has been updated",
                        color: "green"
                    })
                }} mt={10}>Save Changes</Button>
                <Alert mt={15} title="Invoice Variables">
                    The following variables can be used to fill in dynamic customer/invoice infomation.
                    <div className='flex fdc' style={{ gap: 5, marginTop: 10 }}>
                        <TemplateString label='Total' value='[Total]' />
                        <TemplateString label='Date' value='[Date]' />
                        <TemplateString label='Expiry Date' value='[Valid Until]' />
                        <TemplateString label='Invoice Number' value='[Document Number]' />
                        <TemplateString label='Project' value='[Project]' />
                        <TemplateString label='Title' value='[Title]' />
                        <TemplateString label='Currency' value='[Currency]' />
                        <TemplateString label='Last Name / Company' value='[Name 1]' />
                        <TemplateString label='Name' value='[Name]' />
                        <TemplateString label='First Name / Company' value='[Name 2]' />
                        <TemplateString label='User' value='[User]' />
                        <TemplateString label='Email' value='[User Email]' />
                        <TemplateString label='Organisation' value='[Company Name]' />
                        <TemplateString label='Phone Number' value='[Company Phone Nr]' />
                        <TemplateString label='Website' value='[Website]' />

                    </div>
                </Alert>
            </div>
        </div>
    )
}

const Security = () => {
    const [newPassword, setNewPassword] = useState("")
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState("")
    const [credentialModal, setCredentialModal] = useState<{
        open: boolean,
        onSubmit: () => any,
        onCancel: () => any
    }>({ ...defaultCredentialModalValue })


    useEffect(() => {
        if (error) {
            Notifications.show({
                title: "Error",
                message: error,
                color: "red"
            })
        }
        setError("")
    }, [error])


    const attemptUpdatePassword = () => new Promise((res, rej) => {
        updatePassword(auth.currentUser as User, newPassword).then(() => {
            res(undefined)
        }).catch((err) => {
            setLoading(false)
            rej(err.code)
        })
    })

    const changePassword = () => {
        setLoading(true)
        attemptUpdatePassword().then(() => {
            setLoading(false)
            setNewPassword("")
            Notifications.show({
                color: "green",
                title: "Success",
                message: "Chanegs saved"
            })
        }).catch(code => {
            switch (code) {
                case AuthErrorCodes.WEAK_PASSWORD:
                    setError("That password is too weak")
                    break
                case AuthErrorCodes.INVALID_PASSWORD:
                    setError("Invalid password")
                    break
                case AuthErrorCodes.CREDENTIAL_TOO_OLD_LOGIN_AGAIN:
                    setCredentialModal({
                        open: true,
                        onSubmit: () => {
                            attemptUpdatePassword().then(() => {
                                setLoading(false)
                                Notifications.show({
                                    color: "green",
                                    title: "Success",
                                    message: "Chanegs saved"
                                })
                            })
                        },
                        onCancel: () => {
                            setNewPassword("")
                            setLoading(false)
                        }
                    })
                    break
                default:
                    setError("Something went wrong")
                    break
            }
        })
    }

    return (
        <>
            <Modal
                opened={credentialModal.open}
                onClose={() => setCredentialModal({ ...defaultCredentialModalValue })}
                title={<Text fw={600}>Enter your current password</Text>}
                zIndex={30000}
            >
                <CredentialsModal close={() => setCredentialModal({ ...defaultCredentialModalValue })} onSubmit={credentialModal.onSubmit} onCancel={credentialModal.onCancel} />
            </Modal>
            <div>
                <div className='flex' style={{ gap: 10, maxWidth: 800, flexWrap: "wrap" }}>
                    <PasswordInput w={"calc(50% - 10px)"} value={newPassword} onChange={(e) => setNewPassword(e.target.value)} label="New Password" description="Change your password" />
                </div>
                <Button
                    loading={loading}
                    mt={10}
                    disabled={!newPassword}
                    onClick={changePassword}
                >Change Password</Button>
            </div>
        </>
    )
}

const BexioDefaultTax = () => {
    const user = useContext(UserContext)
    const [taxes, setTaxes] = useState<Array<BexioTax>>([])
    const [selectedTax, setSelectedTax] = useState(user.settings?.defaultTax)
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        const getTaxes = () => {
            api("/bexio/get-taxes").then(res => {
                setTaxes(res.data)
            })
        }
        getTaxes()
    }, [])

    return (
        <div>
            <Select label="Default Tax" description="This tax will be applied to all invoice items" value={selectedTax?.toString()} onChange={(e) => e ? setSelectedTax(parseInt(e)) : ""} data={taxes?.map((t) => ({ value: t.id.toString(), label: t.display_name }))} />
            <Button mt={10} loading={loading} disabled={!selectedTax || selectedTax === user.settings?.defaultTax} onClick={async () => {
                setLoading(true)
                await updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                    settings: {
                        ...user.settings,
                        defaultTax: selectedTax
                    }
                } as Partial<UserDoc>)
                setLoading(false)
                Notifications.show({
                    color: "green",
                    title: "Success",
                    message: "Default tax saved"
                })
            }}>Save</Button>
        </div>
    )
}

const BexioDefaultCurrency = () => {
    const user = useContext(UserContext)
    const [currencies, setCurrencies] = useState<BexioCurrency[]>([])
    const [selectedCurrency, setSelectedCurrency] = useState(user.settings?.defaultCurrency)
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        const getCurrencies = async () => {
            api("/bexio/get-currencies").then(res => {
                setCurrencies(res.data)
            })
        }
        getCurrencies()
    }, [])

    return (
        <Box>
            <Select
                label="Default currency"
                description="This currency will be used for all newly created recurring invoices"
                value={selectedCurrency?.toString()}
                onChange={(e) => e ? setSelectedCurrency(parseInt(e)) : void 0}
                data={currencies.map((c) => ({
                    value: c.id.toString(),
                    label: c.name
                }))}
            />
            <Button mt={10} loading={loading} disabled={!selectedCurrency || selectedCurrency === user.settings?.defaultCurrency} onClick={async () => {
                setLoading(true)
                await updateDoc(doc(firestore, "users", auth.currentUser?.uid as string), {
                    settings: {
                        ...user.settings,
                        defaultCurrency: selectedCurrency
                    }
                } as Partial<UserDoc>)
                setLoading(false)
                Notifications.show({
                    color: "green",
                    title: "Success",
                    message: "Default currency saved"
                })
            }}>Save</Button>
        </Box>
    )

}

const BexioAPIKey = () => {
    const [loading, setLoading] = useState(false)
    const router = useRouter()
    return (
        <div>
            <Text fz="sm" fw={500}>Bexio API Key</Text>
            <Text fz={"xs"} c="dimmed">Reset your Bexio API key in the onboarding menu</Text>
            <Button mt={5} variant={"default"} loading={loading} onClick={() => {
                setLoading(true)
                api("/account/reset-bexio-api-key").then(() => {
                    router.push("/setup")
                })
            }}>Reset API Key</Button>
        </div>
    )
}

const Bexio = () => {
    return (
        <div>
            <div style={{ maxWidth: 800, gap: 10 }} className="flex fdc">
                <BexioAPIKey />
                <BexioDefaultTax />
                <BexioDefaultCurrency />
            </div>
        </div>
    )
}

export default function Page() {
    const router = useRouter()
    return (
        <>
            <title>Settings | reBill</title>
            <div>
                <div className='flex aic jcsb'>
                    <Text fw={800} fz={40} style={{ lineHeight: 1 }}>
                        <Text fw={800} fz={20} c="dimmed">
                            Account
                        </Text>
                        Settings
                    </Text>
                </div>
                <Tabs defaultValue={"user"} mt={20} styles={{ panel: { marginTop: 20 }, tabLabel: { fontWeight: 500 } }}>
                    <Tabs.List>
                        <Tabs.Tab value='user'>
                            User Settings
                        </Tabs.Tab>
                        <Tabs.Tab value='templates'>
                            Templates
                        </Tabs.Tab>
                        <Tabs.Tab value='security'>
                            Security
                        </Tabs.Tab>
                        <Tabs.Tab value='bexio'>
                            Bexio
                        </Tabs.Tab>
                        <Tabs.Tab onClick={() => {
                            router.push("/logout")
                        }} c="red" ml={"auto"} leftSection={<Logout size={16} />} color='red' value='logout'>
                            Log Out
                        </Tabs.Tab>
                    </Tabs.List>
                    <Tabs.Panel value='user'>
                        <UserSettings />
                    </Tabs.Panel>
                    <Tabs.Panel value='templates'>
                        <Templates />
                    </Tabs.Panel>
                    <Tabs.Panel value='security'>
                        <Security />
                    </Tabs.Panel>
                    <Tabs.Panel value='bexio'>
                        <Bexio />
                    </Tabs.Panel>

                </Tabs>
            </div >
        </>
    )
}
