import { auth, firestore } from "@/utils/firebase/firebase"
import { collection, getDocs, limit, orderBy, query, where } from "firebase/firestore"
import { useEffect, useState } from "react"
import { DBInvoiceDraft, RecurringInvoice } from "../../../types/global"

export default (docLimit?: number) => {
    const [drafts, setDrafts] = useState<Array<DBInvoiceDraft & { id: string }> | undefined>(undefined)

    const getDrafts = () => {
        const q = query(collection(firestore, "drafts"), where("owner", "==", auth.currentUser?.uid as string), orderBy("created", "desc"), limit(docLimit || 10000000))
        getDocs(q).then((snaps) => {
            const newDrafts = new Array() as Array<DBInvoiceDraft & { id: string }>
            snaps.forEach((snap) => newDrafts.push({ ...snap.data() as DBInvoiceDraft, id: snap.id }))
            setDrafts(newDrafts)
        })
    }

    useEffect(() => {
        getDrafts()
    }, [])

    return { drafts, update: getDrafts }
}