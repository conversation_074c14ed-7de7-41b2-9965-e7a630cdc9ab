import api from "@/utils/api/api"
import { useEffect, useState } from "react"
import { BexioContact } from "../../../types/global"


export default (): { customers: Array<BexioContact> | undefined, reloadCustomers: () => void } => {
    const [customers, setCustomers] = useState<Array<BexioContact> | undefined>(undefined)

    const reloadCustomers = () => {
        setCustomers(undefined)
        api("/bexio/get-contacts").then((res) => {
            if (!res.error) {
                setCustomers(res.data)
            }
        })
    }
    useEffect(() => {
        reloadCustomers()
    }, [])

    return {
        customers,
        reloadCustomers
    }
}