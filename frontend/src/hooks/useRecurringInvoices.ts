import { auth, firestore } from "@/utils/firebase/firebase"
import { collection, getDocs, limit, orderBy, query, where } from "firebase/firestore"
import { useEffect, useState } from "react"
import { RecurringInvoice } from "../../../types/global"

export default (docLimit?: number) => {
    const [invoices, setInvoices] = useState<Array<RecurringInvoice> | undefined>(undefined)

    useEffect(() => {
        const q = query(collection(firestore, "recurringInvoices"), where("ownerID", "==", auth.currentUser?.uid as string), orderBy("created", "desc"), limit(docLimit || 10000000))
        getDocs(q).then((snaps) => {
            const newInvoices = new Array() as Array<RecurringInvoice>
            snaps.forEach((snap) => newInvoices.push({ ...snap.data() as RecurringInvoice, id: snap.id }))
            setInvoices(newInvoices)
        })
    }, [])

    return invoices
}