"use client";
import { LangContext } from '@/context/Lang.context'
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { Language } from '../../../types/global'


export default function LangProvider({ children }: PropsWithChildren) {
    const [lang, setLang] = useState<Language | undefined>()

    useEffect(() => {
        setLang("en")
        // const getLang: () => Promise<Language> = () => new Promise((res) => setTimeout(() => {
        //     res("de")
        // }, 5000))
        // getLang().then((l) => setLang(l))
    }, [])

    if (!lang) return null;

    return (
        <LangContext.Provider value={lang}>
            {children}
        </LangContext.Provider>
    )
}
