import { BexioDataContext } from '@/context/BexioData.contetx'
import api from '@/utils/api/api'
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { BexioDataContextType, BexioTax, BexioUnit } from '../../../types/global'

export default function BexioDataProvider({ children }: PropsWithChildren) {
    const [taxes, setTaxes] = useState<Array<BexioTax> | null>(null)
    const [units, setUnits] = useState<Array<BexioUnit> | null>(null)
    // const [accounts, setAccounts] = useState<Array<BexioAccount> | null>(null)

    const getUnits = () => {
        api("/bexio/get-units").then((res) => {
            setUnits(res.data || [])
        })
    }

    const getTaxes = () => {
        api("/bexio/get-taxes").then(res => {
            setTaxes(res.data || [])
        })
    }

    // const getAccounts = () => {
    //     api("/bexio/get-accounts").then(res => {
    //         setAccounts(res.data || [])
    //     })
    // }

    const getData = async () => {
        getUnits()
        getTaxes()
        // getAccounts()
    }

    useEffect(() => {
        getData()
    }, [])

    useEffect(() => {
        console.log(units, taxes)
    }, [taxes, units])

    if (!taxes || !units) {
        return null
    }
    console.log(taxes, units)
    return (
        <BexioDataContext.Provider value={{
            taxes,
            units,
            accounts: new Array(),
            reload: getData
        } as BexioDataContextType}>
            {children}
        </BexioDataContext.Provider>
    )
}
