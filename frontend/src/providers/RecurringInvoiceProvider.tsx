import { RecurringInvoicesContext } from '@/context/RecurringInvoices.context'
import { auth, firestore } from '@/utils/firebase/firebase'
import { collection, onSnapshot, orderBy, query, where } from 'firebase/firestore'
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { RecurringInvoice } from '../../../types/global'

export default function RecurringInvoiceProvider({ children }: PropsWithChildren) {
    const [invoices, setInvoices] = useState<Array<RecurringInvoice> | undefined>(undefined)

    useEffect(() => {
        const q = query(collection(firestore, "recurringInvoices"), where("ownerID", "==", auth.currentUser?.uid as string), orderBy("created", "desc"))
        const invSnapListener = onSnapshot(q, (snaps) => {
            const newInvoices = new Array()
            snaps.forEach((s) => newInvoices.push({ ...s.data() as RecurringInvoice, id: s.id } as RecurringInvoice))
            setInvoices(newInvoices)
        }, (err) => console.log(err))

        return () => invSnapListener()
    }, [])

    if (!invoices) {
        return null
    }
    return (
        <RecurringInvoicesContext.Provider value={invoices}>
            {children}
        </RecurringInvoicesContext.Provider>
    )
}
