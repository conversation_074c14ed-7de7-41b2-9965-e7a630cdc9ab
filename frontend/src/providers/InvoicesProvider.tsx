import { InvoicesContext } from '@/context/Invoices.context'
import api from '@/utils/api/api'
import { auth, firestore } from '@/utils/firebase/firebase'
import { collection, onSnapshot, query, where } from 'firebase/firestore'
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { BexioInvoice } from '../../../types/global'
import { Box, Text } from '@mantine/core'
import { InfoCircle } from 'tabler-icons-react'

export default function InvoicesProvider({ children }: PropsWithChildren) {
    const [invoices, setInvoices] = useState<Array<BexioInvoice> | undefined>(undefined)
    const [error, setError] = useState("")
    useEffect(() => {
        api("/bexio/get-invoices").then((response) => {
            if (response.error) {
                setInvoices([])
                return setError("Failed to fetch Bexio invoices. Please login with Bexio agian.")
            }
            setInvoices(response.data)
        })
    }, [])

    if (!invoices) {
        return null
    }
    return (
        <InvoicesContext.Provider value={invoices}>
            {error
                ? <>
                    <Box h={65} className='flex aic' style={{ gap: 10, position: "fixed", width: "100%", zIndex: 999, top: 0 }} bg={"red.1"} p={"sm"}>
                        <InfoCircle size={20} color='var(--mantine-color-red-6)' />
                        <Box>
                            <Text lh={1} c="red" fw={600}>Error</Text>
                            <Text fz={"sm"}>

                                {error}
                            </Text>

                        </Box>
                    </Box>
                    <div style={{ marginBottom: 65 }} />
                </>
                : null
            }
            {children}
        </InvoicesContext.Provider>
    )
}
