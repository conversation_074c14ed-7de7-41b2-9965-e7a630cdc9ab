import { DraftsContext } from '@/context/Drafts.context'
import { auth, firestore } from '@/utils/firebase/firebase'
import { collection, onSnapshot, orderBy, query, where } from 'firebase/firestore'
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { DBInvoiceDraft } from '../../../types/global'

export default function DraftsProvider({ children }: PropsWithChildren) {
    const [drafts, setDrafts] = useState<Array<DBInvoiceDraft & { id: string }> | undefined>(undefined)

    useEffect(() => {
        const q = query(collection(firestore, "drafts"), where("owner", "==", auth.currentUser?.uid as string), orderBy("created", "desc"))
        const draftsSnapListener = onSnapshot(q, (snaps) => {
            const newInvoices = new Array()
            snaps.forEach((s) => newInvoices.push({ ...s.data(), id: s.id }))
            setDrafts(newInvoices)
        }, (err) => console.log(err))

        return () => draftsSnapListener()
    }, [])

    if (!drafts) {
        return null
    }
    return (
        <DraftsContext.Provider value={drafts}>
            {children}
        </DraftsContext.Provider>
    )
}
