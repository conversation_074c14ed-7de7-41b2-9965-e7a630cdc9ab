import { Paper, Text } from "@mantine/core"
import CountUp from 'react-countup';

export const Stat = ({ label, value, dark }: { label: string, value: any, dark?: boolean }) => {
    return (
        <Paper p="sm" bg={dark ? "var(--mantine-primary-color-filled)" : "#f9f9f9"} style={{ flex: 1 }}>
            <Text fw={600} c="dimmed" fz="sm">{label}</Text>
            <Text c={dark ? "#fff" : "#000"} fw={700} fz={20}>
                {typeof value === "number"
                    ? <CountUp
                        end={value}
                    />
                    : <>{value}</>
                }
            </Text>
        </Paper>
    )
}