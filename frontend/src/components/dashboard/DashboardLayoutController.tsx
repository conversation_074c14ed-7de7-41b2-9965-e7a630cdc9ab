"use client"
import { Box } from '@mantine/core'
import { usePathname } from 'next/navigation'
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { DashboardHeader } from '../global/Headers'
import Sidebar from './Sidebar'

export default function DashboardLayoutController({ children }: PropsWithChildren) {
    const [sidebarOpen, setSidebarOpen] = useState(false)
    const pathname = usePathname()

    useEffect(() => {
        setSidebarOpen(false)
    }, [pathname])
    return (
        <div className='flex' style={{ overflowX: "hidden", height: "100vh", overflow: "hidden" }}>
            <nav style={{ position: "absolute", background: "#f9f9f9", height: "100%", width: 300, transition: "all .3s", transform: `translateX(${sidebarOpen ? 0 : -300}px)`, boxShadow: !sidebarOpen ? "none" : "0px 0px 10px rgba(0,0,0,.1)" }}>
                <Sidebar />
            </nav>
            <div style={{ transform: `translateX(${sidebarOpen ? 300 : 0}px)`, transition: "all .3s", flex: 1 }} className="flex fdc">
                <DashboardHeader sidebarOpen={sidebarOpen} toggleSidebar={(open) => setSidebarOpen(open)} />
                <Box component={"main"} p="md" style={{ overflowY: "auto", flex: 1 }}>
                    {children}
                </Box>
            </div>
        </div>
    )
}
