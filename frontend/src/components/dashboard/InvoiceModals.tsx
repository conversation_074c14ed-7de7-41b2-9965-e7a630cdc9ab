import React, { ButtonHTMLAttributes, ReactHTMLElement, useContext, useEffect, useState } from 'react'
import { ActionIcon, Alert, Box, Button, ButtonGroup, Collapse, Divider, HoverCard, Modal, NumberInput, Paper, SegmentedControl, Select, Skeleton, Text, Textarea, TextInput, Tooltip } from '@mantine/core'
import { ArrowRight, Check, CircleCheck, InfoCircle, Plus, Receipt, Trash, User, X } from 'tabler-icons-react'
import { BexioContact, BexioTax, BexioTaxStatus, BexioUnit, BillingPeriod, DBInvoiceDraft, DraftCustomerDetails, DraftInvoiceDetails, DraftItem } from '../../../../types/global'
import useCustomers from '@/hooks/useCustomers'
import Error from '../global/Error'
import api from '@/utils/api/api'
import { addDoc, collection, doc, updateDoc } from 'firebase/firestore'
import { auth, firestore } from '@/utils/firebase/firebase'
import { notifications } from '@mantine/notifications';
import { DateInput } from '@mantine/dates';
import { BexioDataContext } from '@/context/BexioData.contetx'
import { UserContext } from '@/utils/context/User.context'

const CreateCustomerForm = ({ updateFieldWithNewCustomer }: { updateFieldWithNewCustomer: (id: string) => void }) => {
    const [loading, setLoading] = useState(false)
    const [success, setSuccess] = useState(false)
    const [error, setError] = useState("")

    const [newCustomer, setNewCustomer] = useState<DraftCustomerDetails>({
        name: "",
        address: "",
        city: "",
        email: "",
        type: 1,
        postcode: ""
    })

    const updateNewCustomer = (newInfo: Partial<DraftCustomerDetails>) => setNewCustomer((prev) => ({ ...prev, ...newInfo }))

    return (
        <div>
            <Text fw={500}>New Customer</Text>
            <Divider my={5} />
            <div className='flex aic' style={{ gap: 10, marginTop: 10 }}>
                <TextInput value={newCustomer.name} onChange={(e) => updateNewCustomer({ name: e.target.value })} required style={{ flex: 1 }} placeholder='Joe Smith' label='Name' />
                <TextInput value={newCustomer.email} onChange={(e) => updateNewCustomer({ email: e.target.value })} required style={{ flex: 1 }} placeholder='<EMAIL>' label='Email' />
            </div>
            <div style={{ marginTop: 10 }}>
                {/* @ts-ignore */}
                <Select required label="Customer type" data={[{ value: "1", label: "Company" }, { value: "2", label: "Individual" }]} value={newCustomer.type.toString()} onChange={(e) => updateNewCustomer({ type: e !== undefined ? parseInt(e) : 1 })} />
            </div>
            <div style={{ marginTop: 10 }}>
                <TextInput value={newCustomer.address} onChange={(e) => updateNewCustomer({ address: e.target.value })} label="Address" placeholder='123 Commercial Street' />
                <div className='flex aic' style={{ gap: 10, marginTop: 5 }}>
                    <TextInput value={newCustomer.city} onChange={(e) => updateNewCustomer({ city: e.target.value })} style={{ flex: 1 }} label="City" placeholder='London' />
                    <TextInput value={newCustomer.postcode} onChange={(e) => updateNewCustomer({ postcode: e.target.value })} style={{ flex: 1 }} label="Post Code" placeholder='N16 9NS' />
                </div>
            </div>
            <Button loading={loading} mt={15} fullWidth disabled={!newCustomer.name || !newCustomer.email} onClick={() => {
                setLoading(true)
                setError("")
                setSuccess(false)

                api("/bexio/create-customer", {
                    customer: { ...newCustomer }
                }).then((res) => {
                    if (res.error) {
                        return setError(res.msg)
                    }
                    updateFieldWithNewCustomer(res.data)
                    setSuccess(true)
                }).finally(() => {
                    setLoading(false)
                })
            }}>Create Customer</Button>
            <Text fz={"xs"} mt={5} c="dimmed">You can add more infomation in the Bexio dashboard</Text>
            <Collapse mt={10} in={success}>
                <Alert color={"green"} title="Customer Created">
                    You can now select your new customer in the existing customers tab
                </Alert>
            </Collapse>
            <Error mt={10}>{error}</Error>
        </div>
    )
}

const ExistingCustomerForm = ({ invoiceDetails, updateDetails }: { invoiceDetails: DraftInvoiceDetails, updateDetails: (newDetails: Partial<DraftInvoiceDetails>) => void }) => {
    const { customers } = useCustomers()
    return (
        <div>
            {customers
                ? <Select
                    value={invoiceDetails.customerID}
                    onChange={(e) => {

                        const details = e ? customers.find((c) => parseInt(c.id as unknown as string) == parseInt(e as string)) as BexioContact : null
                        console.log(details)
                        updateDetails({
                            customerID: e || "",
                            customerDetails: details ? {
                                name: `${details?.name_1 || ""} ${details?.name_2 || ""}`,
                                email: details.mail,
                                pfp: ""
                            } : null
                        })
                    }}
                    placeholder='Select a customer'
                    leftSection={<User size={17} />}
                    // label="Customer"
                    // description="Choose a customer from your Bexio contact base"
                    data={customers.map((c) => ({ value: c.id.toString(), label: c.name_1 }))}
                    searchable
                    clearable
                />
                : <Skeleton w="100%" h={36} />
            }
        </div>
    )
}

const CustomerInput = ({ invoiceDetails, updateDetails }: { invoiceDetails: DraftInvoiceDetails, updateDetails: (newDetails: Partial<DraftInvoiceDetails>) => void }) => {
    const [type, setType] = useState<"new" | "existing">("existing")

    return (
        <>
            <div className='flex aic jcsb'>
                <div>
                    <Text fz={"md"} fw={500}>Choose a Customer</Text>
                    <Text fz={"xs"} c="dimmed">Select an existing customer or create a new one</Text>
                </div>
                <SegmentedControl onClick={() => console.log("Clicked")} value={type} onChange={(e) => { console.log("changed"); setType(e as "new" | "existing") }} w={"fit-content"} data={[{ value: "existing", label: "Existing" }, { value: "new", label: "New" }]} />
            </div>
            {/* <Divider variant={"dashed"} /> */}
            {type === "existing"
                ? <ExistingCustomerForm invoiceDetails={invoiceDetails} updateDetails={updateDetails} />
                : <Paper p="md" bg={"#f9f9f9"}>
                    <CreateCustomerForm updateFieldWithNewCustomer={(newID) => {
                        console.log("Calling")
                        updateDetails({ customerID: newID })
                    }} />
                </Paper>
            }

        </>
    )
}

const Item = ({ units, item, updateItem, removeItem, i }: { units: Array<BexioUnit>, taxes: Array<BexioTax>, item: DraftItem, updateItem: (item: DraftItem) => void, removeItem: () => void, i: number }) => {
    const user = useContext(UserContext)
    const [name, setName] = useState(item.name)
    const [quantity, setQuantity] = useState(item.unitQ)
    const [unit, setUnit] = useState(item.unitID)
    const [tax, setTax] = useState(item.tax || user.settings.defaultTax || 0)
    const [unitPrice, setUnitPrice] = useState(item.unitPrice)
    const { accounts, taxes } = useContext(BexioDataContext)

    console.log(name)

    useEffect(() => {
        if (name !== item.name || quantity !== item.unitQ || unit !== item.unitID || unitPrice !== item.unitPrice || tax !== item.tax) {
            updateItem({
                name,
                unitQ: quantity,
                unitID: unit,
                unitPrice,
                tax: tax
            })
        }
    }, [name, quantity, unit, unitPrice, tax])

    useEffect(() => {
        setName(item.name)
        setQuantity(item.unitQ)
        setUnit(item.unitID)
        setUnitPrice(item.unitPrice)
    }, [item])


    return (
        <Box className='flex jcsb' style={{ gap: 5 }}>
            <Textarea placeholder='Item Name' style={{ flex: 2 }} value={name} onChange={(e) => setName(e.target.value)} />
            <NumberInput min={0} style={{ flex: 1 }} value={quantity} onChange={(e) => setQuantity(e as number)} />
            <Select placeholder='Select unit' value={unit.toString()} onChange={(e) => setUnit(e as unknown as number)} style={{ flex: 1 }} data={units.map((u) => ({ value: u.id.toString(), label: u.name }))} />
            <NumberInput style={{ flex: 1 }} value={unitPrice} onChange={(e) => setUnitPrice(e as number)} />
            <Select
                allowDeselect={false}
                style={{ flex: 1 }}
                value={tax?.toString()}
                // @ts-ignore
                onChange={setTax}
                data={taxes?.map((a) => ({ value: a?.id.toString() as string, label: `${a?.display_name.toString()}${user.settings.defaultTax == a?.id ? "(default)" : ""}` as string })) || []}
            />
            <div style={{ flex: 1, gap: 5 }} className="flex">
                <Paper bg={"#f9f9f9"} withBorder h={36} className="flex aic" style={{ flex: 1 }}>
                    <Text style={{ padding: "0 10px" }} fw={500}>{unitPrice * quantity}</Text>
                </Paper>
                <ActionIcon variant={"subtle"} color="gray" onClick={removeItem} disabled={!i}><Trash size={20} /></ActionIcon>
            </div>
        </Box>
    )
}

const emptyItem = { name: "", unitID: 0, unitPrice: 0, unitQ: 0, tax: 0 } as DraftItem
const ItemsController = ({ invoiceItems, setInvoiceItems }: { invoiceItems: Array<DraftItem>, setInvoiceItems: (items: Array<DraftItem>) => void }) => {

    const [items, setItems] = useState<Array<DraftItem>>([...invoiceItems])
    const { taxes, units } = useContext(BexioDataContext)
    // const [units, setUnits] = useState<Array<BexioUnit>>([])
    // const [taxes, setTaxes] = useState<Array<BexioTax>>([])

    useEffect(() => {
        setInvoiceItems(items)
    }, [items])

    // const getUnits = () => {
    //     api("/bexio/get-units").then((res) => {
    //         setUnits(res.data)
    //     })
    // }

    // const getTaxes = () => {
    //     api("/bexio/get-taxes").then(res => {
    //         setTaxes(res.data)
    //     })
    // }

    // useEffect(() => {
    //     getUnits()
    //     getTaxes()
    // }, [])


    const removeItem = (i: number) => {
        const newItems = [...items]
        newItems.splice(i, 1)
        setItems(newItems)
    }

    const updateItem = (item: DraftItem, i: number) => {
        const newItems = [...items]
        newItems[i] = item
        setItems(newItems)
    }


    return (
        <Box style={{ border: "1px solid rgba(0,0,0,.1)", width: "100%", borderRadius: 10 }}>
            <Box className='flex aic jcsb' p={"sm"} style={{ gap: 5 }}>
                <div style={{ flex: 2, gap: 5 }} className="flex aic">
                    <Text fz={"sm"} fw={600} c="dimmed">Name</Text>
                    <HoverCard withArrow shadow={"md"} >
                        <HoverCard.Target>
                            <ActionIcon size={'xs'} variant="subtle">
                                <InfoCircle size={13} color={"var(--mantine-color-dimmed)"} />
                            </ActionIcon>
                        </HoverCard.Target>
                        <HoverCard.Dropdown p="xs">
                            {/* <Box style={{ boxShadow: "0px 0px 10px rgba(0,0,0,.1)", color: "#000" }} bg="#fff"> */}
                            <div>
                                <Text fw={500} fz="sm">Invoice Variables</Text>
                                <Text c="dimmed" fz="xs">Use these variables to display dynamic infomation in your item names</Text>
                                <Divider my={5} />
                                <Text fz={"sm"}>
                                    <strong>{"[year]"}</strong> - Current Year (e.g. {new Date().getFullYear()}) <br />
                                    <strong>{"[month]"}</strong> - Current Month (e.g. {new Date().getMonth() + 1}) <br />
                                    <strong>{"[nextyear]"}</strong> - Next Year (e.g. {new Date().getFullYear() + 1}) <br />
                                    <strong>{"[nextmonth]"}</strong> - Next Month (e.g. {new Date().getMonth() + 2}) <br />
                                </Text>
                            </div>
                            {/* </Box> */}

                        </HoverCard.Dropdown>
                    </HoverCard>
                </div>
                <Text fz={"sm"} style={{ flex: 1 }} fw={600} c="dimmed">Unit Quanity</Text>
                <Text fz={"sm"} style={{ flex: 1 }} fw={600} c="dimmed">Unit</Text>
                <Text fz={"sm"} style={{ flex: 1 }} fw={600} c="dimmed">Unit Price</Text>
                <Text fz={"sm"} style={{ flex: 1 }} fw={600} c="dimmed">Tax</Text>
                <Text fz={"sm"} style={{ flex: 1 }} fw={600} c="dimmed">Total</Text>
            </Box>
            <Divider />
            <Box className='flex fdc' style={{ gap: 7 }} p={"sm"}>
                {items.map((item, i) => {
                    return <Item taxes={taxes} i={i} key={i} item={item} removeItem={() => {
                        removeItem(i)
                    }} updateItem={(item) => {
                        updateItem(item, i)
                    }} units={units} />
                })}
            </Box>
            <Box p="sm" pt={0}>
                <Button variant={"subtle"} leftSection={<Plus size={16} />} size="compact-sm" onClick={() => {
                    const newItems = [...items]
                    newItems.push({ ...emptyItem })
                    setItems(newItems)
                }}>Add item</Button>
            </Box>

            <Divider />
            <Box p="sm">
                <Text fw={600} fz={"lg"}>Total: <Text span fw={700}>{items.map((i) => isNaN(i.unitPrice * i.unitQ) ? 0 : i.unitPrice * i.unitQ).reduce((partialSum, a) => partialSum + a, 0)}</Text></Text>
            </Box>
        </Box>
    )
}

enum TaxStatus {
    INCLUDING_TAXES = 0,
    EXCLUDING_TAXES = 1,
    EXEMPT_FROM_TAXES = 2
}

const TaxEditor = ({ taxStatus, setTaxStatus }: { taxStatus: BexioTaxStatus, setTaxStatus: (e: BexioTaxStatus) => void }) => {
    const [status, setStatus] = useState<BexioTaxStatus>(taxStatus)

    useEffect(() => {
        setTaxStatus(status)
    }, [status])

    return (
        <Box>
            <Select required allowDeselect={false} value={status?.toString()} onChange={(e) => setStatus(parseInt(e as string) as BexioTaxStatus)} label="Tax Status" description="Select this invoice's tax status"
                data={[
                    { label: "Includes taxes", value: TaxStatus.INCLUDING_TAXES.toString() },
                    { label: "Excluding taxes", value: TaxStatus.EXCLUDING_TAXES.toString() },
                    { label: "Exempt from taxes", value: TaxStatus.EXEMPT_FROM_TAXES.toString() },
                ]}
            />
        </Box>
    )
}

const SaveDraftButton = ({ invoiceDetails, close, draftID, setDraftID }: { invoiceDetails: Partial<DraftInvoiceDetails>, close: () => void, draftID?: string, setDraftID: (newID: string) => void }) => {
    const [loading, setLoading] = useState(false)
    return (
        <Button loading={loading} size={"md"} variant="default" onClick={async () => {
            setLoading(true)
            if (draftID) {
                await updateDoc(doc(firestore, "drafts", draftID), {
                    draft: {
                        ...invoiceDetails,
                        taxStatus: invoiceDetails.taxStatus || null,
                        startDate: invoiceDetails.startDate
                    }
                } as Partial<DBInvoiceDraft>)
                setLoading(false)
                notifications.show({
                    color: "blue",
                    title: "Draft Saved",
                    message: `Your existing draft (#${draftID}) has been updated`,
                    icon: <Check />
                })
                return
            }
            addDoc(collection(firestore, "drafts"), {
                draft: {
                    ...invoiceDetails
                },
                owner: auth.currentUser?.uid,
                created: Date.now()
            } as DBInvoiceDraft).then((newDoc) => {
                setLoading(false)
                notifications.show({
                    color: "green",
                    title: "Draft Saved",
                    message: "View and edit drafts in the dashboard",
                    icon: <Check />
                })
                setDraftID(newDoc.id)
            }).catch((err) => {
                console.log(err)
                setLoading(false)
                notifications.show({
                    color: "red",
                    title: "Draft not saved",
                    message: "Something went wrong. Please try again.",
                    icon: <X />
                })
            })

        }}>Save Draft</Button>
    )
}

const CreateButton = ({ draft, close }: { draft: DraftInvoiceDetails, close: () => void }) => {
    const [loading, setLoading] = useState(false)
    return (
        <Button size={"md"} leftSection={<Receipt size={20} />} loading={loading} onClick={() => {
            setLoading(true)

            const status = notifications.show({
                loading: true,
                title: "Creating your invoice",
                message: "This should not take long",
                autoClose: false,
                withCloseButton: false
            })

            api("/bexio/create-invoice", { invoice: draft }).then((response) => {
                if (response.error) {
                    notifications.update({
                        id: status,
                        color: "red",
                        title: "Error",
                        message: response.msg,
                        icon: <X />,
                        loading: false,
                        autoClose: 6500
                    })
                    return setLoading(false)
                }
                notifications.update({
                    id: status,
                    color: "green",
                    title: "Invoice created",
                    message: "Your invoice was created successfully.",
                    icon: <Check />,
                    loading: false,
                    autoClose: 6500
                })
                setLoading(false)
                close()
            })
        }}>Create Invoice</Button>
    )
}


export const InvoiceEditorForm = ({ close, draftData, draftID, customAction, customHeader }: {
    close: () => void,
    draftData?: Partial<DraftInvoiceDetails>,
    draftID?: string,
    customAction?: (invoiceDetails: DraftInvoiceDetails) => React.ReactElement,
    customHeader?: () => React.ReactElement
}) => {

    const [statefulDraftID, setDraftID] = useState(draftID || "")

    const [invoiceDetials, setInvoiceDetails] = useState<DraftInvoiceDetails>(draftData as DraftInvoiceDetails || {
        title: "",
        customerID: "",
        description: "",
        period: "monthly",
        items: [{ ...emptyItem }],
        startDate: Date.now(),
        taxStatus: 0
    })


    const updateDetials = (newDetails: Partial<DraftInvoiceDetails>) => setInvoiceDetails((prev) => ({ ...prev, ...newDetails }))

    return (
        <div className='flex aic jcc'>
            <Box maw={1000} w={"100%"} h={200}>
                {customHeader
                    ? <>{customHeader()}</>
                    : <div className='flex aic jcsb' style={{ width: "100%" }}>
                        <div className='flex aic' style={{ gap: 5 }}>
                            <Receipt size={20} />
                            <Text fw={600}>{statefulDraftID ? `Editing draft #${statefulDraftID}` : "Create a new reccuring invoice"}</Text>
                        </div>
                        <ActionIcon onClick={close} variant={"subtle"} color="gray"><X color='#000' size={20} /></ActionIcon>
                    </div>
                }

                <Box w={"100%"} style={{ gap: 20, marginTop: 10, paddingBottom: 50 }} className="flex fdc">
                    <div>
                        <Divider my={10} />
                        <Text fz={"xl"} fw={600}>Invoice Details</Text>
                        <div className='flex' style={{ gap: 10, marginTop: 5, width: "100%" }}>
                            {/* Internal Titles */}
                            <div style={{ gap: 10, flex: 1 }} className="flex fdc">
                                <TextInput required value={invoiceDetials.rebill_title} onChange={(e) => updateDetials({ rebill_title: e.target.value })} label="Title in reBill" description="A short title to describe your invoice" placeholder='e.g. Construction services...' />
                                <Textarea value={invoiceDetials.rebill_description} onChange={(e) => updateDetials({ rebill_description: e.target.value })} label="Description in reBill" description="Describe the productservices you're providing" placeholder='e.g. On-going development services...' />
                            </div>
                            {/* Bexio Titles */}
                            <div style={{ gap: 10, flex: 1 }} className="flex fdc">
                                <TextInput value={invoiceDetials.title} onChange={(e) => updateDetials({ title: e.target.value })} label="Title in Bexio" description="A short title to describe your invoice" placeholder='e.g. Construction services...' />
                                <Textarea value={invoiceDetials.reference} onChange={(e) => updateDetials({ reference: e.target.value })} label="Reference in Bexio" description="Describe the productservices you're providing" placeholder='e.g. On-going development services...' />
                            </div>
                        </div>

                    </div>

                    <div>
                        <Divider my={10} />
                        <Text fz={"xl"} fw={600}>Billing</Text>
                        <div style={{ marginTop: 5, gap: 10 }} className="flex fdc">
                            <CustomerInput invoiceDetails={invoiceDetials} updateDetails={updateDetials} />
                            <Select required value={invoiceDetials.period} onChange={(e) => updateDetials({ period: e as BillingPeriod })} mt={5} label='Billing Period' description="Your customer will be billed on this cycle" data={[{ label: "Monthly", value: "monthly" as BillingPeriod }, { label: "Yearly", value: "yearly" as BillingPeriod }]} />
                            <DateInput required minDate={new Date()} value={new Date(invoiceDetials.startDate) as Date} onChange={(d) => updateDetials({ startDate: d ? Date.parse(d.toDateString()) : Date.now() })} label="Start Date" description="Invoices will be sent to your customer starting on this date" />
                        </div>
                    </div>

                    <div>
                        <Divider my={10} />
                        <Text fz={"xl"} fw={600}>Items</Text>
                        <div style={{ marginTop: 5, gap: 10 }} className="flex fdc">
                            <ItemsController invoiceItems={invoiceDetials.items} setInvoiceItems={(items) => updateDetials({ items: items })} />
                        </div>
                    </div>
                    <div>
                        <Divider my={10} />
                        <Text fz={"xl"} fw={600}>Tax</Text>
                        <TaxEditor taxStatus={invoiceDetials.taxStatus} setTaxStatus={(s) => updateDetials({ taxStatus: s })} />
                    </div>
                    <div>
                        <Divider my={10} />
                        {customAction
                            ? customAction(invoiceDetials)
                            : <div className='flex aic' style={{ gap: 10 }}>
                                <CreateButton close={close} draft={invoiceDetials} />
                                <SaveDraftButton draftID={statefulDraftID} setDraftID={setDraftID} invoiceDetails={invoiceDetials} close={close} />
                            </div>
                        }
                    </div>
                </Box>
            </Box>
        </div>
    )
}

export const DraftInvoiceModal = ({ target, draft, draftID }: { target: React.ReactElement, draft: Partial<DraftInvoiceDetails>, draftID: string }) => {
    const [open, setOpen] = useState(false)

    return (
        <>
            <Modal
                opened={open}
                onClose={() => setOpen(false)}
                fullScreen
                withCloseButton={false}
            >
                <InvoiceEditorForm draftData={draft} draftID={draftID} close={() => setOpen(false)} />
            </Modal>
            {React.cloneElement(target, { onClick: () => setOpen(true) } as ButtonHTMLAttributes<any>)}
        </>
    )
}

export default function CreateInvoiceModal({ target }: { target: React.ReactElement }) {
    const [open, setOpen] = useState(false)

    return (
        <>
            <Modal
                opened={open}
                onClose={() => setOpen(false)}
                fullScreen
                withCloseButton={false}
            >
                <InvoiceEditorForm close={() => setOpen(false)} />
            </Modal>
            {React.cloneElement(target, { onClick: () => setOpen(true) } as ButtonHTMLAttributes<any>)}
        </>
    )
}
