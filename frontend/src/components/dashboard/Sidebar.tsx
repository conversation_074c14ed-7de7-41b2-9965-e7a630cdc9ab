import { Box, Text } from '@mantine/core'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'
import { File, Home, Receipt, Settings } from 'tabler-icons-react'

const routes = [
    {
        label: "Dashboard",
        href: "/",
        icon: <Home />
    },
    {
        label: "Invoices",
        href: "/invoices",
        icon: <Receipt />
    },
    {
        label: "Drafts",
        href: "/drafts",
        icon: <File />
    },
    {
        label: "Settings",
        href: "/settings",
        icon: <Settings />
    }
] as Array<{
    icon: React.ReactElement,
    href: string,
    label: string
}>
export default function Sidebar() {
    const path = usePathname()
    return (
        <div style={{ padding: 15, gap: 5 }} className="flex fdc" >
            {routes.map((r) => {
                const active = `${path}` === `${r.href}`
                return (
                    <Link href={`${r.href}`}>
                        <Box bg={active ? "#fff" : "transparent"} w="100%" p={"sm"} className="flex aic cp" style={{
                            gap: 15,
                            borderRadius: 10,
                            transition: "all .2s",
                            transform: `scale(${active ? 1 : .95})`,
                            opacity: active ? 1 : .5,
                            boxShadow: `0px 0px 10px rgba(0,0,0,${active ? ".1" : 0})`
                        }}>
                            {React.cloneElement(r.icon, { size: 22 })}
                            <Text fw={500}>{r.label}</Text>
                        </Box>
                    </Link>
                )
            })}
        </div>
    )
}
