"use client"
import Error from '@/components/global/Error'
import { auth } from '@/utils/firebase/firebase'
import { Button, Paper, PasswordInput } from '@mantine/core'
import { EmailAuthProvider, reauthenticateWithCredential, updateEmail, updatePassword, User } from 'firebase/auth'
import React, { useState } from 'react'

export const CredentialsModal = ({ onSubmit, onCancel, close }: { onSubmit: () => void, onCancel: () => void, close: () => void }) => {
    const [password, setPassword] = useState("")
    const [error, setError] = useState("")
    const [loading, setLoading] = useState(false)
    return (
        <Paper>
            <PasswordInput placeholder='Enter your password' value={password} onChange={(e) => setPassword(e.target.value)} />
            <div className='flex aic' style={{ gap: 10, marginTop: 10 }}>
                <Button onClick={onCancel} style={{ flex: 1 }} variant="default">
                    Cancel
                </Button>
                <Button loading={loading} disabled={!password} style={{ flex: 1 }} onClick={() => {
                    setLoading(true)
                    const credential = EmailAuthProvider.credential(
                        auth.currentUser?.email as string,
                        password
                    )
                    reauthenticateWithCredential(auth.currentUser as User, credential).then(() => {
                        auth.currentUser?.reload()
                        console.log("HEre", onSubmit)
                        close()
                        onSubmit()
                    }).catch((err) => {
                        console.log(err)
                        setError("Incorrect password")
                    }).finally(() => setLoading(false))
                }}>Continue</Button>
            </div>
            <Error mt={10}>{error}</Error>
        </Paper>
    )
}