"use client"
import { ActionIcon, <PERSON>, <PERSON>, <PERSON><PERSON>, Divider, Image, Paper } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import Link from 'next/link'
import React, { useContext, useEffect, useState } from 'react'
import { UserCircle } from 'tabler-icons-react'
import Logo from './Logo'
import { LangContext } from '@/context/Lang.context'
import translations from '../translations.json'
import { UserContext } from '@/utils/context/User.context'
export const DefaultHeader = () => {
    const lang = useContext(LangContext)
    return (
        <div style={{ padding: 10, position: "absolute", width: "100%" }}>
            <Paper withBorder component={"header"} className='flex aic jcsb' style={{ width: "100%", border: "3px solid #000", boxShadow: "-3px 5px 0px #000", padding: 15, height: 80 }}>
                <Logo href='/' />
                <div className='flex aic' style={{ gap: 5 }}>
                    <Link href={"/auth/login"}>
                        <Button variant={"default"}>{translations.headers.default.login[lang]}</Button>
                    </Link>
                    <Link href={"/auth/signup"}>
                        <Button >{translations.headers.default.signup[lang]}</Button>
                    </Link>
                </div>
            </Paper>
        </div>
    )
}

export const DashboardHeader = ({ toggleSidebar, sidebarOpen }: { toggleSidebar: (open: boolean) => void, sidebarOpen: boolean }) => {
    const user = useContext(UserContext)
    const [open, setOpen] = useState(false);

    useEffect(() => {
        if (sidebarOpen !== open) {
            toggleSidebar(open)
        }
    }, [open])

    useEffect(() => {
        setOpen(sidebarOpen)
    }, [sidebarOpen])

    return (
        <Box p="md" w={"100%"} className='flex aic jcsb' component={"header"}>
            <div className='flex aic' style={{ gap: 15 }}>
                {/* <ActionIcon variant={"outline"} size="lg"> */}
                <Burger size={"md"} opened={open} onClick={() => setOpen(!open)} />
                {/* </ActionIcon> */}
                <Box className='flex aic' style={{ gap: 18 }}>
                    <Logo href='/' />
                    {user.company?.pfp
                        ? <>
                            <Divider opacity={.5} orientation="vertical" />
                            <Image src={`${user.company.pfp}`} w={100} />
                        </>
                        : null
                    }
                </Box>
            </div>
            <Link href="/settings">
                <ActionIcon size={"lg"} variant={"subtle"}>
                    <UserCircle size={30} />
                </ActionIcon>
            </Link>
        </Box>
    )
}