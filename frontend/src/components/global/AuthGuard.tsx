"use client"
import { UserContext } from '@/utils/context/User.context'
import { auth, firestore } from '@/utils/firebase/firebase'
import { onAuthStateChanged } from 'firebase/auth'
import { doc, getDoc } from 'firebase/firestore'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { PropsWithChildren, useEffect, useState } from 'react'
import { User } from '../../../../types/global'

export default function AuthGuard({ children, loading }: PropsWithChildren & { loading?: boolean }) {
    const [user, setUser] = useState<User & { update: (newValues: Partial<User>) => void } | undefined>()
    const router = useRouter()
    useEffect(() => {
        const unsub = onAuthStateChanged(auth, usr => {
            if (usr) {
                console.log(usr.uid)
                getDoc(doc(firestore, "users", usr.uid)).then((snap) => {
                    const userData = snap.data() as User
                    setUser({
                        ...userData,
                        update(newValues) {
                            // @ts-ignore
                            setUser((prev: User) => ({ ...prev, ...newValues }))
                        },
                    })
                }).catch((err) => console.log(err))
            } else {
                router.push("/auth/login")
            }
        })

        return () => unsub()
    }, [])

    if (!user || loading) {
        return (
            <div style={{ width: "100%", height: "100%" }} className="flex aic jcc">
                <Image alt='reBill Logo' width={150} height={150} src="/logo/logo.gif" />
            </div>
        )
    }
    return (
        <UserContext.Provider value={user}>{children}</UserContext.Provider>
    )
}
