import { Text } from '@mantine/core'
import Link from 'next/link'
import React from 'react'

export default function Logo({ size, href, light }: { size?: number, href?: string, light?: boolean }) {
    const Base = () => <Text fw={900} className="cp" c={light ? "#fff" : "#000"} fz={size || 30}>reBill</Text>
    if (href) {
        return (
            <Link href={href}>
                <Base />
            </Link>
        )
    }
    return <Base />

}
