import { Language } from "../../../../types/global";
import EN from '../../data/templates/email/en.json'
import DE from '../../data/templates/email/de.json'
import FR from '../../data/templates/email/fr.json'


export const getEmailTemplate = (language: Language) => {
    switch (language) {
        case "en":
            return EN
        case "de":
            return DE
        default:
            return EN
    }
}