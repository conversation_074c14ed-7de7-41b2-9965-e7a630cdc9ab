import { RecurringInvoice } from "../../../../types/global"

export const getMRR = (invoices: Array<RecurringInvoice>) => {
    return Math.round(
        invoices.map((i) => (
            i.status !== "active" ? 0 :
                i.items.map(
                    (it) => it.unitPrice * it.unitQ).reduce((a, b) => a + b) / (i.period === "monthly" ? 1 : 12
                ))
        ).reduce((a, b) => a + b, 0)
    ).toLocaleString("en-GB")
}

export const getYRR = (invoices: Array<RecurringInvoice>) => {
    return Math.round(
        invoices.map((i) => (
            i.status !== "active" ? 0 :
                i.items.map(
                    (it) => it.unitPrice * it.unitQ).reduce((a, b) => a + b) * (i.period === "yearly" ? 1 : 12
                ))
        ).reduce((a, b) => a + b, 0)
    ).toLocaleString("en-GB")
}