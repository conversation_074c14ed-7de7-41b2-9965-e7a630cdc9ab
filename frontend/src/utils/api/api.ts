import axios from 'axios';
import { APIResponse } from '../../../../types/global';
import { auth } from '../firebase/firebase';

const API_BASE_URL =
    process.env.NODE_ENV === 'production'
        ? 'https://rebill-caf96.ew.r.appspot.com'
        : 'http://localhost:8080';

const api = async (path: string, body?: object | FormData, authenticate?: boolean) => {
    const url = `${API_BASE_URL}${path}`;

    let xAuth = undefined
    if ((authenticate !== false) && auth.currentUser) {
        const token = await auth.currentUser.getIdToken(true).catch((err) => console.log(err))
        xAuth = token
    }

    return axios.post(url, body || {}, {
        headers: {
            "x-auth": xAuth
        } as any
    }).then((response) => {
        // if (response.data.redirect) {
        //     window.location.replace(response.data.redirect)
        // }
        return response.data as APIResponse;
    }).catch((err) => {
        console.log(err, url)
        return {
            error: true,
            msg: `Something went wrong on our end ${err.response?.status ? `(${err.response?.status})` : ""}`,
            data: null
        } as APIResponse
    });

    // return response.data;
};

export default api