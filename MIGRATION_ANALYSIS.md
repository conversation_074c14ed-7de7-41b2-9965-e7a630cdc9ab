# Analisis <PERSON> ke <PERSON> 11 + Blade

## Struktur Proyek Lama
- **Frontend**: Next.js (React)
- **Backend**: Node.js/TypeScript
- **Integrasi**: 
  - Bexio API
  - Firebase (akan dihilangkan)
  - Google Cloud (diabaikan sementara)

## Target Arsitektur Baru
- **Frontend**: Blade templates
- **Backend**: Lara<PERSON> 11 (PHP)
- **Database**: MySQL/MariaDB
- **Autentikasi**: Bexio OAuth

## Komponen Penting
1. **Autentikasi Bexio OAuth**
   - Menggunakan Socialite + custom provider
   - Menyimpan token akses di database
   - Flow: Redirect → Callback → Auth

2. **Struktur Database**
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bexio_id VARCHAR(255) UNIQUE,
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE,
    bexio_access_token TEXT,
    bexio_refresh_token TEXT,
    bexio_token_expires_at TIMESTAMP
);

CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT FOREIGN KEY REFERENCES users(id),
    bexio_invoice_id VARCHAR(255),
    data JSON,
    created_at TIMESTAMP
);
```

3. **Package yang Diperlukan**
- laravel/socialite
- socialiteproviders/bexio (custom)
- mysql/mariadb driver

## Langkah Implementasi
1. Setup Laravel 11
2. Konfigurasi database MySQL
3. Implementasi Bexio OAuth
4. Migrasi view dari Next.js ke Blade
5. Porting business logic dari Node.js ke Laravel

## Catatan
- Integrasi GCloud sementara dihilangkan
- Firebase diganti dengan MySQL
- Prioritas pada autentikasi Bexio dan manajemen invoice
