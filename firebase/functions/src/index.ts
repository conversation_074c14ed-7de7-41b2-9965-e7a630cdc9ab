import { getFirestore } from "firebase-admin/firestore";
import { User } from '../../../types/global'
import { UserProvider } from 'firebase-admin/auth'
/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */
const functions = require('firebase-functions/v1');
const admin = require("firebase-admin")
// import {onRequest} from "firebase-functions/v2/https";
// import * as logger from "firebase-functions/logger";
// import {
//     onDocumentCreated
// } from 'firebase-functions/v2/firestore'
admin.initializeApp()

exports.createUser = functions.auth.user().onCreate(async (user: UserProvider) => {
    const db = getFirestore()
    return db.collection("users").doc(user.uid as string).set({
        name: user.displayName,
        email: user.email,
        created: Date.now(),
        settings: {

        }
    } as User)
    // ...
});
