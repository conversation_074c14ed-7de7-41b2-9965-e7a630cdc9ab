# Database Integration & Seeding

## 💾 Database Structure Implementation

### **Existing Database Schema**

#### **Tables Analysis:**
```sql
-- Users table (existing)
users:
- id (primary key)
- bexio_id (string)
- name (string)
- email (string)
- bexio_access_token (text)
- bexio_refresh_token (text)
- bexio_token_expires_at (timestamp)

-- Invoices table (existing)
invoices:
- id (primary key)
- user_id (foreign key)
- bexio_id (string)
- document_nr (string)
- contact_info (json)
- total (decimal)
- status (enum: draft, sent, paid, cancelled)
- is_recurring (boolean)
- recurring_settings (json)

-- Recurring templates table (existing)
recurring_templates:
- id (primary key)
- user_id (foreign key)
- invoice_id (foreign key)
- interval (enum: daily, weekly, monthly)
- next_run (timestamp)
```

### **Data Seeding Implementation**

#### **1. Invoice Seeder Creation**
**File**: `laravel-app/database/seeders/InvoiceSeeder.php`

**Demo User Creation:**
```php
$user = User::create([
    'bexio_id' => '12345',
    'name' => 'Demo User',
    'email' => '<EMAIL>',
    'bexio_access_token' => 'mock_access_token',
    'bexio_refresh_token' => null,
    'bexio_token_expires_at' => now()->addHours(1),
]);
```

**Sample Invoice Data:**
```php
$invoices = [
    [
        'user_id' => $user->id,
        'bexio_id' => '1001',
        'document_nr' => 'INV-2024-1001',
        'contact_info' => [
            'name' => 'Acme Corporation',
            'email' => '<EMAIL>',
            'address' => '123 Business St, Zurich, Switzerland'
        ],
        'total' => 2500.00,
        'status' => 'sent',
        'is_recurring' => true,
        'recurring_settings' => [
            'interval' => 'monthly',
            'next_charge' => now()->addMonth()->format('Y-m-d')
        ]
    ],
    // ... 4 more sample invoices
];
```

#### **2. Recurring Templates Integration**
**Automatic Template Creation:**
```php
foreach ($invoices as $invoiceData) {
    $invoice = Invoice::create($invoiceData);
    
    if ($invoice->is_recurring) {
        RecurringTemplate::create([
            'user_id' => $user->id,
            'invoice_id' => $invoice->id,
            'interval' => $invoice->recurring_settings['interval'],
            'next_run' => $invoice->recurring_settings['next_charge']
        ]);
    }
}
```

### **Model Enhancements**

#### **1. User Model Updates**
**File**: `laravel-app/app/Models/User.php`

**Added DateTime Casting:**
```php
protected $casts = [
    'email_verified_at' => 'datetime',
    'bexio_token_expires_at' => 'datetime', // Fixed casting issue
];
```

#### **2. RecurringTemplate Model**
**File**: `laravel-app/app/Models/RecurringTemplate.php`

**Model Configuration:**
```php
class RecurringTemplate extends Model
{
    protected $fillable = [
        'user_id',
        'invoice_id', 
        'interval',
        'next_run'
    ];

    protected $casts = [
        'next_run' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}
```

### **Service Layer Integration**

#### **1. BexioMockService Enhancement**
**File**: `laravel-app/app/Services/BexioMockService.php`

**Database-Driven Invoice Retrieval:**
```php
public function getInvoices(): array
{
    $user = User::where('email', '<EMAIL>')->first();
    
    if (!$user) {
        return [];
    }

    $invoices = Invoice::where('user_id', $user->id)
        ->orderBy('created_at', 'desc')
        ->get();

    return $invoices->map(function ($invoice) {
        return [
            'id' => $invoice->bexio_id,
            'document_nr' => $invoice->document_nr,
            'title' => $invoice->contact_info['name'] ?? 'Invoice',
            'total' => $invoice->total,
            'is_valid' => $invoice->status !== 'cancelled',
            'status' => $invoice->status,
            'contact_info' => $invoice->contact_info,
            'is_recurring' => $invoice->is_recurring,
            'recurring_settings' => $invoice->recurring_settings,
        ];
    })->toArray();
}
```

**Recurring Invoices Method:**
```php
public function getRecurringInvoices(): array
{
    $user = User::where('email', '<EMAIL>')->first();
    
    $recurringInvoices = Invoice::where('user_id', $user->id)
        ->where('is_recurring', true)
        ->with('recurringTemplate')
        ->orderBy('created_at', 'desc')
        ->get();

    return $recurringInvoices->map(function ($invoice) {
        return [
            'id' => $invoice->id,
            'rebill_title' => $invoice->contact_info['name'] ?? 'Recurring Invoice',
            'rebill_description' => "Invoice for {$invoice->contact_info['name']}",
            'status' => $invoice->status === 'cancelled' ? 'inactive' : 'active',
            'period' => $invoice->recurringTemplate->interval ?? 'monthly',
            'nextCharge' => $invoice->recurringTemplate ? 
                $invoice->recurringTemplate->next_run->timestamp * 1000 : 
                now()->addMonth()->timestamp * 1000,
            'total' => $invoice->total,
        ];
    })->toArray();
}
```

**Drafts Method:**
```php
public function getDrafts(): array
{
    $user = User::where('email', '<EMAIL>')->first();
    
    $drafts = Invoice::where('user_id', $user->id)
        ->where('status', 'draft')
        ->orderBy('created_at', 'desc')
        ->get();

    return $drafts->map(function ($invoice) {
        return [
            'id' => $invoice->id,
            'created' => $invoice->created_at->timestamp * 1000,
            'draft' => [
                'rebill_title' => $invoice->contact_info['name'] ?? '',
                'rebill_description' => "Draft invoice for {$invoice->contact_info['name']}",
                'customerDetails' => [
                    'name' => $invoice->contact_info['name'] ?? '',
                    'email' => $invoice->contact_info['email'] ?? '',
                    'address' => $invoice->contact_info['address'] ?? ''
                ],
                'total' => $invoice->total
            ]
        ];
    })->toArray();
}
```

### **Data Migration & Seeding**

#### **Migration Status:**
```bash
php artisan migrate:status
# Result: All migrations ran successfully
```

#### **Seeding Execution:**
```bash
php artisan db:seed --class=InvoiceSeeder
# Result: 5 invoices + 3 recurring templates created
```

### **Database Relationships**

#### **Relationship Mapping:**
```php
// User Model
public function invoices()
{
    return $this->hasMany(Invoice::class);
}

public function recurringTemplates()
{
    return $this->hasMany(RecurringTemplate::class);
}

// Invoice Model
public function user()
{
    return $this->belongsTo(User::class);
}

public function recurringTemplate()
{
    return $this->hasOne(RecurringTemplate::class);
}
```

### **Data Validation & Integrity**

#### **Database Constraints:**
- Foreign key constraints properly set
- JSON field validation for contact_info
- Enum constraints for status and interval fields
- Decimal precision for monetary values

#### **Model Validation:**
```php
// Invoice creation validation
$request->validate([
    'document_nr' => 'required|string|max:255',
    'contact_name' => 'required|string|max:255',
    'contact_email' => 'required|email|max:255',
    'total' => 'required|numeric|min:0',
    'status' => 'required|in:draft,sent,paid,cancelled',
]);
```

### **Performance Considerations**

#### **Query Optimization:**
- **Eager Loading**: Use `with()` for relationships
- **Indexing**: Proper indexes on foreign keys
- **Pagination**: Implement for large datasets
- **Caching**: Cache frequently accessed data

#### **Database Efficiency:**
```php
// Efficient query with relationships
$invoices = Invoice::where('user_id', $user->id)
    ->with('recurringTemplate')
    ->orderBy('created_at', 'desc')
    ->paginate(10);
```

### **Testing & Verification**

#### **Data Verification:**
```bash
# Check seeded data
php artisan tinker
>>> App\Models\Invoice::count()
=> 5
>>> App\Models\RecurringTemplate::count()
=> 3
```

#### **Relationship Testing:**
```php
// Test relationships work correctly
$invoice = Invoice::with('recurringTemplate')->first();
$template = $invoice->recurringTemplate;
// Result: Relationship loads correctly
```

### **Future Database Enhancements**

#### **Potential Improvements:**
1. **Audit Trail**: Track invoice changes
2. **Soft Deletes**: Recoverable deletions
3. **Full-Text Search**: Advanced search capabilities
4. **Data Archiving**: Historical data management
5. **Backup Strategy**: Automated backups
