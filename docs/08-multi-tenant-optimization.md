# Multi-Tenant SaaS Optimization

## 🏢 Multi-Tenant Architecture Implementation

### **Requirements Analysis**
Based on <PERSON>'s specifications, the application needed to be transformed from a single-user system to a multi-tenant SaaS platform with:

1. **Organization-based Access Control**
2. **Subscription Management**
3. **Trial Period Management**
4. **Admin Portal Capabilities**
5. **Enhanced Bexio Integration**

### **Database Schema Changes**

#### **1. Organizations Table**
```sql
CREATE TABLE organizations (
    id BIGINT PRIMARY KEY,
    bexio_organization_id VARCHAR(255) UNIQUE,
    name VARCHAR(255),
    email VARCHAR(255),
    country VARCHAR(255) DEFAULT 'CH',
    language VARCHAR(255) DEFAULT 'de',
    status ENUM('trial', 'active', 'inactive') DEFAULT 'trial',
    trial_ends_at TIMESTAMP NULL,
    activated_at TIMESTAMP NULL,
    bexio_company_profile JSON NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### **2. Subscriptions Table**
```sql
CREATE TABLE subscriptions (
    id BIGINT PRIMARY KEY,
    organization_id BIGINT FOREIGN KEY,
    plan_type ENUM('monthly', 'yearly') DEFAULT 'monthly',
    price DECIMAL(8,2),
    status ENUM('trial', 'active', 'cancelled', 'expired') DEFAULT 'trial',
    trial_ends_at TIMESTAMP NULL,
    current_period_start TIMESTAMP NULL,
    current_period_end TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    payment_method VARCHAR(255) NULL,
    billing_details JSON NULL,
    notes TEXT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### **3. Updated Users Table**
```sql
ALTER TABLE users ADD COLUMN (
    organization_id BIGINT FOREIGN KEY,
    role ENUM('user', 'admin', 'super_admin') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    bexio_user_profile JSON NULL
);
```

### **Model Relationships**

#### **Organization Model Features:**
```php
// Relationships
public function users() // hasMany
public function subscription() // hasOne
public function invoices() // hasMany
public function recurringTemplates() // hasMany

// Business Logic
public function isActive()
public function isTrial()
public function isTrialExpired()
public function canCreateInvoices()
public function getDaysLeftInTrial()
```

#### **User Model Enhancements:**
```php
// New Relationships
public function organization() // belongsTo

// Role Management
public function isAdmin()
public function isSuperAdmin()
public function canManageOrganization()

// Subscription Checks
public function hasActiveSubscription()
public function canCreateInvoices()
```

### **Authentication Flow Enhancement**

#### **Multi-Tenant OAuth Process:**
1. **User Login via Bexio OAuth**
2. **Retrieve User + Company Data**
3. **Create/Update Organization**
4. **Assign User to Organization**
5. **Set Trial Period (3 months)**
6. **Create Default Subscription**
7. **Send Welcome Email**

#### **Enhanced BexioAuthController:**
```php
public function callback()
{
    return DB::transaction(function () use ($tokenData, $userData, $companyData) {
        // Create/update organization
        $organization = Organization::updateOrCreate([...]);
        
        // Set trial period for new orgs
        if ($organization->wasRecentlyCreated) {
            $organization->update(['trial_ends_at' => now()->addMonths(3)]);
        }
        
        // Create/update user with organization
        $user = User::updateOrCreate([...]);
        
        // Create subscription if needed
        if (!$organization->subscription) {
            Subscription::create([...]);
        }
        
        // Send welcome email for new organizations
        if ($organization->wasRecentlyCreated) {
            $this->sendWelcomeEmail($user, $organization);
        }
    });
}
```

### **Access Control Implementation**

#### **Organization-Based Data Isolation:**
```php
// All queries scoped to organization
public function scopeForOrganization($query, $organizationId)
{
    return $query->where('organization_id', $organizationId);
}

// Middleware for organization access
class EnsureOrganizationAccess
{
    public function handle($request, Closure $next)
    {
        if (!auth()->user()->organization) {
            abort(403, 'No organization access');
        }
        
        return $next($request);
    }
}
```

#### **Role-Based Permissions:**
```php
// User roles: user, admin, super_admin
public function canManageOrganization()
{
    return $this->isAdmin() || $this->isSuperAdmin();
}

// First user in organization becomes admin
private function determineUserRole($userData, $organization)
{
    return $organization->users()->count() === 0 ? 'admin' : 'user';
}
```

### **Subscription Management**

#### **Trial Management:**
- **3 months free trial** for new organizations
- **Automatic trial expiration** checking
- **Grace period** handling
- **Upgrade prompts** in UI

#### **Subscription States:**
```php
// Subscription status flow
'trial' → 'active' → 'cancelled'/'expired'

// Business logic
public function canCreateInvoices()
{
    return $this->isActive() || ($this->isTrial() && !$this->isTrialExpired());
}
```

#### **Pricing Model:**
- **Monthly Plan**: CHF 29/month
- **Yearly Plan**: CHF 29*12 - discount = CHF 300/year
- **Trial**: 3 months free

### **Admin Portal Requirements**

#### **Super Admin Capabilities:**
1. **View All Organizations**
   - Organization list with status
   - Usage statistics
   - Subscription details

2. **Manage Organization Status**
   - Activate/deactivate organizations
   - Extend trial periods
   - Manual subscription management

3. **Billing Management**
   - View subscription status
   - Process upgrade requests
   - Handle cancellations

#### **Organization Admin Capabilities:**
1. **User Management**
   - Invite users to organization
   - Manage user roles
   - Deactivate users

2. **Organization Settings**
   - Update company profile
   - Manage Bexio integration
   - View usage statistics

### **Email & Notification System**

#### **Welcome Email Flow:**
```php
// Triggered on new organization creation
private function sendWelcomeEmail($user, $organization)
{
    // Email content:
    // - Welcome message
    // - Trial period info (3 months)
    // - Pricing information
    // - Getting started guide
    // - Support contact
}
```

#### **Subscription Emails:**
- **Trial Expiration Warning** (7 days before)
- **Upgrade Request** (when user clicks upgrade)
- **Subscription Confirmation**
- **Payment Reminders**

### **Security Enhancements**

#### **Data Isolation:**
- **Organization-scoped queries** for all data
- **User access validation** on every request
- **Token management** per organization
- **Audit logging** for admin actions

#### **API Security:**
- **Rate limiting** per organization
- **Token refresh** automation
- **Secure credential storage**
- **CSRF protection** on all forms

### **Performance Optimizations**

#### **Database Optimizations:**
```sql
-- Indexes for multi-tenant queries
INDEX(organization_id, status)
INDEX(organization_id, is_recurring)
INDEX(trial_ends_at)
INDEX(current_period_end)
```

#### **Caching Strategy:**
- **Organization data** caching
- **User permissions** caching
- **Subscription status** caching
- **Bexio API responses** caching

### **Migration Strategy**

#### **Existing Data Migration:**
1. **Create default organization** for existing users
2. **Migrate existing invoices** to organization
3. **Set up trial subscriptions** for existing orgs
4. **Update user roles** (first user = admin)

#### **Deployment Steps:**
1. **Run new migrations**
2. **Execute data migration script**
3. **Update application code**
4. **Test multi-tenant functionality**
5. **Deploy to production**

### **Future Enhancements**

#### **Advanced Features:**
1. **White-label Solutions**
2. **Custom Branding** per organization
3. **Advanced Analytics** and reporting
4. **API Access** for integrations
5. **Mobile Application** support

#### **Scaling Considerations:**
1. **Database sharding** by organization
2. **Microservices** architecture
3. **CDN integration** for assets
4. **Load balancing** strategies
5. **Monitoring** and alerting
