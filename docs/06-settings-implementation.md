# Settings Page Implementation

## ⚙️ User Settings Management System

### **Feature Overview**
Comprehensive settings page with tabbed interface for profile management, security, and Bexio integration.

### **1. Settings Controller Development**

#### **File**: `laravel-app/app/Http/Controllers/SettingsController.php`

**Key Methods:**
```php
class SettingsController extends Controller
{
    public function index()                    // Show settings page
    public function updateProfile()           // Update user profile
    public function updatePassword()          // Change password
    public function updateBexioSettings()     // Manage Bexio integration
    public function resetBexioConnection()    // Disconnect Bexio
}
```

**Security Features:**
- Password verification for changes
- CSRF protection on all forms
- User authorization checks
- Secure token handling

### **2. Settings Page Structure**

#### **File**: `laravel-app/resources/views/settings/index.blade.php`

**Tab-based Interface:**
1. **Profile Tab**: Name and email management
2. **Security Tab**: Password change functionality
3. **Bexio Integration Tab**: Connection management

**Navigation Header:**
```php
<ul class="nav nav-tabs" id="settingsTabs">
    <li class="nav-item">
        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#profile">
            <i class="fas fa-user me-1"></i>Profile
        </button>
    </li>
    <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#security">
            <i class="fas fa-lock me-1"></i>Security
        </button>
    </li>
    <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#bexio">
            <i class="fas fa-link me-1"></i>Bexio Integration
        </button>
    </li>
    <li class="nav-item ms-auto">
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <button type="submit" class="nav-link text-danger border-0 bg-transparent">
                <i class="fas fa-sign-out-alt me-1"></i>Logout
            </button>
        </form>
    </li>
</ul>
```

### **3. Profile Management**

#### **Profile Update Form:**
```php
<form method="POST" action="{{ route('settings.profile.update') }}">
    @csrf
    @method('PUT')
    
    <div class="row g-3">
        <div class="col-md-6">
            <label for="name" class="form-label">Full Name</label>
            <input type="text" class="form-control" id="name" name="name" 
                   value="{{ old('name', $user->name) }}" required>
        </div>
        <div class="col-md-6">
            <label for="email" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="email" name="email" 
                   value="{{ old('email', $user->email) }}" required>
        </div>
    </div>

    <div class="mt-3">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i>Save Changes
        </button>
    </div>
</form>
```

**Controller Logic:**
```php
public function updateProfile(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255|unique:users,email,' . Auth::id(),
    ]);

    $user = Auth::user();
    $user->update([
        'name' => $request->name,
        'email' => $request->email,
    ]);

    return redirect()->route('settings.index')->with('success', 'Profile updated successfully!');
}
```

### **4. Security Management**

#### **Password Change Form:**
```php
<form method="POST" action="{{ route('settings.password.update') }}">
    @csrf
    @method('PUT')
    
    <div class="row g-3">
        <div class="col-md-6">
            <label for="current_password" class="form-label">Current Password</label>
            <input type="password" class="form-control" id="current_password" name="current_password" required>
        </div>
        <div class="col-md-6">
            <label for="new_password" class="form-label">New Password</label>
            <input type="password" class="form-control" id="new_password" name="new_password" required>
        </div>
        <div class="col-md-6">
            <label for="new_password_confirmation" class="form-label">Confirm New Password</label>
            <input type="password" class="form-control" id="new_password_confirmation" name="new_password_confirmation" required>
        </div>
    </div>

    <div class="mt-3">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-key me-1"></i>Update Password
        </button>
    </div>
</form>
```

**Password Validation Logic:**
```php
public function updatePassword(Request $request)
{
    $request->validate([
        'current_password' => 'required',
        'new_password' => 'required|min:8|confirmed',
    ]);

    $user = Auth::user();

    if (!Hash::check($request->current_password, $user->password)) {
        return back()->withErrors(['current_password' => 'Current password is incorrect.']);
    }

    $user->update([
        'password' => Hash::make($request->new_password),
    ]);

    return redirect()->route('settings.index')->with('success', 'Password updated successfully!');
}
```

### **5. Bexio Integration Management**

#### **Connection Status Display:**
```php
<div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
    <div>
        <h6 class="mb-1">Connection Status</h6>
        <small class="text-muted">
            @if($user->bexio_access_token)
                <span class="badge bg-success">Connected</span>
                Connected as {{ $user->name }}
            @else
                <span class="badge bg-warning">Not Connected</span>
                Please connect your Bexio account
            @endif
        </small>
    </div>
    <div>
        @if($user->bexio_access_token)
            <form method="POST" action="{{ route('settings.bexio.reset') }}">
                @csrf
                <button type="submit" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-unlink me-1"></i>Disconnect
                </button>
            </form>
        @else
            <a href="{{ route('bexio.login') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-link me-1"></i>Connect Bexio
            </a>
        @endif
    </div>
</div>
```

#### **Integration Details:**
```php
@if($user->bexio_access_token)
<div class="alert alert-info">
    <h6 class="alert-heading">
        <i class="fas fa-info-circle me-1"></i>Integration Details
    </h6>
    <p class="mb-2"><strong>Bexio ID:</strong> {{ $user->bexio_id }}</p>
    <p class="mb-2"><strong>Token Expires:</strong> 
        @if($user->bexio_token_expires_at)
            {{ $user->bexio_token_expires_at->format('M j, Y g:i A') }}
        @else
            Never
        @endif
    </p>
    <hr>
    <p class="mb-0">Your Bexio account is connected and ready to sync invoices.</p>
</div>
@endif
```

### **6. Route Configuration**

#### **File**: `laravel-app/routes/web.php`

**Settings Routes:**
```php
Route::middleware('auth')->group(function () {
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings/profile', [SettingsController::class, 'updateProfile'])->name('settings.profile.update');
    Route::put('/settings/password', [SettingsController::class, 'updatePassword'])->name('settings.password.update');
    Route::put('/settings/bexio', [SettingsController::class, 'updateBexioSettings'])->name('settings.bexio.update');
    Route::post('/settings/bexio/reset', [SettingsController::class, 'resetBexioConnection'])->name('settings.bexio.reset');
});
```

### **7. Error Handling & Validation**

#### **Named Error Bags:**
```php
@if ($errors->updateProfile->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach ($errors->updateProfile->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

@if ($errors->updatePassword->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach ($errors->updatePassword->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
```

#### **Success Messages:**
```php
@if (session('success'))
    <div class="alert alert-success alert-dismissible fade show">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if (session('info'))
    <div class="alert alert-info alert-dismissible fade show">
        {{ session('info') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif
```

### **8. User Model Enhancement**

#### **DateTime Casting Fix:**
**Issue**: `bexio_token_expires_at` was causing format() errors

**Solution**: Added proper casting in User model
```php
protected $casts = [
    'email_verified_at' => 'datetime',
    'bexio_token_expires_at' => 'datetime', // Fixed casting issue
];
```

### **9. Bexio Integration Features**

#### **Connection Management:**
```php
public function resetBexioConnection()
{
    $user = Auth::user();
    $user->update([
        'bexio_access_token' => null,
        'bexio_refresh_token' => null,
        'bexio_token_expires_at' => null,
    ]);

    return redirect()->route('bexio.login')->with('info', 'Please reconnect your Bexio account.');
}
```

#### **Token Management:**
```php
public function updateBexioSettings(Request $request)
{
    $request->validate([
        'bexio_access_token' => 'nullable|string',
        'bexio_refresh_token' => 'nullable|string',
    ]);

    $user = Auth::user();
    $user->update([
        'bexio_access_token' => $request->bexio_access_token,
        'bexio_refresh_token' => $request->bexio_refresh_token,
        'bexio_token_expires_at' => $request->bexio_access_token ? now()->addHour() : null,
    ]);

    return redirect()->route('settings.index')->with('success', 'Bexio settings updated successfully!');
}
```

### **10. UI/UX Features**

#### **Responsive Design:**
- Mobile-friendly tab navigation
- Proper form spacing and layout
- Touch-friendly buttons and inputs
- Accessible form labels and ARIA attributes

#### **Visual Feedback:**
- Loading states for form submissions
- Success/error message styling
- Status badges for connection state
- Icon usage for better UX

#### **Navigation Integration:**
```php
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h6 class="text-muted mb-1">Account</h6>
        <h1 class="display-5 fw-bold mb-0">Settings</h1>
    </div>
    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
    </a>
</div>
```

### **11. Testing Results**

#### **Functionality Tests:**
- ✅ Profile updates work correctly
- ✅ Password changes with validation
- ✅ Bexio connection management functions
- ✅ Error handling displays properly
- ✅ Success messages show correctly
- ✅ Navigation flows smoothly

#### **Security Tests:**
- ✅ Password verification required
- ✅ CSRF protection active
- ✅ User authorization enforced
- ✅ Token handling secure
- ✅ Form validation prevents invalid data

### **12. Comparison with Next.js**

| Feature | Next.js | Laravel | Status |
|---------|---------|---------|---------|
| Profile Management | ✅ Firebase Auth | ✅ Laravel Auth | ✅ |
| Password Change | ✅ Firebase | ✅ Hash verification | ✅ |
| Bexio Integration | ✅ API key mgmt | ✅ OAuth token mgmt | ✅ |
| Tab Interface | ✅ Mantine tabs | ✅ Bootstrap tabs | ✅ |
| Error Handling | ✅ Client/Server | ✅ Server-side | ✅ |
| Responsive Design | ✅ Mobile-first | ✅ Bootstrap responsive | ✅ |
