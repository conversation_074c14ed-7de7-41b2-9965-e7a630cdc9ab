{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start:dev": "npx nodemon", "start": "node ./build/index.js", "gcp-build": "tsc -p ."}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/cors": "^2.8.15", "@types/node": "^20.8.7", "nodemon": "^3.0.1", "ts-node": "^10.9.1"}, "dependencies": {"@google-cloud/storage": "^7.15.2", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.0"}}