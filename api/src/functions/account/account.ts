// ! DEPRECATED
// import { Request, Response } from "express";
// import { removeKey, setKey } from "../../utils/auth/apiKey";

// export const saveAPIKey = async (req: Request, res: Response) => {
//     const { uid } = res.locals
//     const { key } = req.body

//     await setKey(uid, key)

//     res.send({
//         error: false,
//         data: "Key saved successfully"
//     })
// }

// export const resetAPIKey = async (_: Request, res: Response) => {
//     const { uid } = res.locals

//     await removeKey(uid)

//     res.send({
//         error: false,
//         redirect: "/setup"
//     })
// }