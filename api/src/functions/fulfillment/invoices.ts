import { Request, Response } from "express"
import { FieldValue, getFirestore } from "firebase-admin/firestore"
import { BexioContact, BexioCurrency, BexioInvoice, RecurringInvoice, User } from "../../types/global"
import bexio from "../../utils/bexio"
import { createBexioInvoice, sendInvoice } from "../../utils/bexio/invoice"
import periods from "../../utils/bexio/periods"
import invoiceVariableParser from "../../utils/helpers/invoiceVariableParser"
import { generateAccessToken } from "../../utils/auth/bexioApiKeymiddleware"

export const fulfillInvoices = async (_: Request, res: Response) => {
    const db = getFirestore()

    const pendingInvoices = new Array() as Array<RecurringInvoice & { id: string }>

    const pendingInvoiceSnapshots = await db.collection("recurringInvoices").where("nextCharge", "<", Date.now()).where("status", "==", "active").get().then(snaps => snaps)
    pendingInvoiceSnapshots.forEach(snap => pendingInvoices.push({ ...snap.data() as RecurringInvoice, id: snap.id }))

    const writeBatch = db.batch()

    let failed = 0
    let fulfilled = 0

    for (let i = 0; i < pendingInvoices.length; i++) {
        const invoice = pendingInvoices[i]
        const user = await (db.collection("users").doc(invoice.ownerID).get().then((snap) => snap.data())) as User
        const access_token = await generateAccessToken(invoice.ownerID).catch(() => null)

        if (!access_token) continue;

        if (!invoice.error_retries ? false : invoice.error_retries >= 3) {
            writeBatch.update(db.collection("recurringInvoices").doc(invoice.id), {
                history: FieldValue.arrayUnion({
                    error: {
                        message: `We failed to send your invoice after 3 attempts. Please update your invoice to try again`,
                        timestamp: Date.now()
                    },
                }) as unknown as Array<BexioInvoice>,
                error_retries: invoice.error_retries ? FieldValue.increment(1) : 1
            } as Partial<RecurringInvoice>)
            failed++
            continue
        }
        const customer = await bexio.get({
            path: `/contact/${invoice.customerID}`,
            access_token,
        }) as BexioContact

        if (!invoice.currency) { // Currency is a new field
            writeBatch.update(db.collection("recurringInvoices").doc(invoice.id), {
                currency: user.settings.defaultCurrency || 1
            } as Partial<RecurringInvoice>)
            invoice.currency = user.settings.defaultCurrency || 1
        }

        const currency = await bexio.get({
            path: `/currencies/${invoice.currency}`,
            access_token,
        }) as BexioCurrency

        await createBexioInvoice(access_token, {
            customerID: invoice.customerID,
            reference: invoiceVariableParser(invoice.reference, invoice, currency.name),
            items: invoice.items.map((item) => (
                { ...item, name: invoiceVariableParser(item.name, invoice, currency.name) }
            )),
            rebill_title: invoice.title,
            rebill_description: invoice.rebill_description,
            period: invoice.period,
            title: invoiceVariableParser(invoice.title, invoice, currency.name),
            startDate: invoice.startDate,
            taxStatus: invoice.taxStatus,
            customerDetails: {
                email: customer.mail,
                name: `${customer.name_1} ${customer.name_2 || ""}`,
                pfp: customer.profile_image || ""
            }
        }, invoice.taxID, invoice.currency || 1).then(async (response) => {
            const new_invoice = response as BexioInvoice

            await bexio.get({
                path: `/contact/${new_invoice.contact_id}`,
                access_token,
            }).then(async (response) => {
                const contact = response as BexioContact
                await sendInvoice(access_token, new_invoice.id, contact?.mail as unknown as string, { body: invoiceVariableParser(user.settings?.templates?.email.body, invoice, currency.name), subject: invoiceVariableParser(user.settings?.templates?.email.subject, invoice, currency.name) })

                writeBatch.update(db.collection("recurringInvoices").doc(invoice.id), {
                    // We'll update the cached customer details here
                    customerDetails: {
                        email: contact.mail,
                        name: contact.name_1,
                        pfp: contact.profile_image
                    },
                    invoiceID: new_invoice.id.toString(),
                    nextCharge: invoice.nextCharge + periods[invoice.period],
                    history: FieldValue.arrayUnion({ ...new_invoice, rebill_timestamp: Date.now() }) as unknown as Array<BexioInvoice>,
                    updated: Date.now()
                } as Partial<RecurringInvoice>)

                fulfilled++
            }).catch((err) => {
                console.log("Error (customer catch):", err)
                writeBatch.update(db.collection("recurringInvoices").doc(invoice.id), {
                    history: FieldValue.arrayUnion({
                        error: {
                            message: `We failed to send your invoice. Make sure the target contact has an email address attached to them. We'll try again 2 more times.`,
                            timestamp: Date.now()
                        },
                    }) as unknown as Array<BexioInvoice>,
                    error_retries: invoice.error_retries ? FieldValue.increment(1) : 1
                } as Partial<RecurringInvoice>)
                failed++
            })
        }).catch((err) => {
            console.log("Error (create catch):", err)
            writeBatch.update(db.collection("recurringInvoices").doc(invoice.id), {
                history: FieldValue.arrayUnion({
                    error: {
                        message: `We failed to create a Bexio invoice. This is probably an issue with your invoice settings. We'll try again an hour from now.`,
                        timestamp: Date.now()
                    },
                }) as unknown as Array<BexioInvoice>
            } as Partial<RecurringInvoice>)
            failed++
        })
    }

    await writeBatch.commit()

    const statusMsg = `Fulfillment complete: Failed: ${failed} Fulfilled: ${fulfilled}`
    console.log(statusMsg)
    if (res) {
        res.status(200).send(statusMsg)
    }
}