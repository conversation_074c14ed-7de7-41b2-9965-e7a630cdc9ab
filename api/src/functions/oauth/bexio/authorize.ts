import { Request, Response } from "express";
import { storage } from "../../../utils/gcloud/storage";
import axios from "axios";
import { db } from "../../../utils/firebase/firebase";
import { User } from "../../../types/global";
import { setToken } from "../../../utils/auth/apiKey";

export const authorizeBexio = async (req: Request, res: Response) => {
    const { code } = req.body
    const { uid } = res.locals

    axios.post(`https://auth.bexio.com/realms/bexio/protocol/openid-connect/token`, {
        code: code,
        grant_type: "authorization_code",
        client_id: "1f4ac48c-53c7-4bee-96ca-eac904753097",
        client_secret: "P4HKi6e96mKov9kcGbaNjT3gVfDgoV5dB7Jd_m_JWYmv7LHFJ2syA0DQVgMvlpQoh9T6AKwKjdMFSo3NEj-tOw",
        redirect_uri: `${process.env.NODE_ENV === "production" ? "https://app.rebill.ch" : "http://localhost:3000"}/oauth/bexio`
    }, {
        headers: {
            "Content-Type": "application/x-www-form-urlencoded"
        }
    }).then(async (response) => {
        const { refresh_token } = response.data
        setToken(uid, refresh_token)

        res.send({
            error: false,
            data: "Authorized"
        })
    }).catch(err => {
        console.dir(err.response.data, { depth: null })
        res.send({
            error: true,
            msg: err?.response?.data?.error_description || "Something went wrong"
        })
    })
}