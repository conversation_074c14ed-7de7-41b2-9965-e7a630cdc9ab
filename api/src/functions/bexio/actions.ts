import { Request, Response } from "express";
import { BexioContact, BexioInvoice, BexioInvoiceCreatePayload, DBInvoiceDraft, DraftCustomerDetails, DraftInvoiceDetails, RecurringInvoice, User } from "../../types/global";
import { getToken } from "../../utils/auth/apiKey";
import bexio from "../../utils/bexio";
import { getFirestore } from 'firebase-admin/firestore'
import { createBexioInvoice } from "../../utils/bexio/invoice";
import getAndValidateRecurringInvoice from "../../utils/data/getAndValidateRecurringInvoice";

export const createCustomer = async (req: Request, res: Response) => {
    const { uid } = res.locals
    const { customer } = req.body as { customer: DraftCustomerDetails }
    const { access_token } = res.locals

    bexio.post({
        path: `/contact`,
        body: {
            contact_type_id: customer.type,
            name_1: customer.name,
            address: customer.address,
            postcode: isNaN(parseInt(customer.postcode)) ? undefined : parseInt(customer.postcode),
            city: customer.city,
            owner_id: 1,
            user_id: 1,
            mail: customer.email
        } as Partial<BexioContact>,
        access_token
    }).then((response: any) => {
        res.send({
            error: false,
            data: response?.id
        })
    }).catch((err) => {
        console.log(err)
        res.send({
            error: true,
            msg: "Something went wrong. We were not able to add your new customer. Please try again"
        })
    })
}

export const createInvoice = async (req: Request, res: Response) => {
    const { uid } = res.locals
    const { invoice, draftID } = req.body as { invoice: DraftInvoiceDetails, draftID: string }
    const db = getFirestore()

    const userData = await (db.collection("users").doc(uid).get().then((snap) => snap.data())) as User
    const { access_token } = res.locals
    const customerData: BexioContact = await bexio.get({
        path: `/contact/${invoice.customerID}`,
        access_token
    })

    if (!userData.settings?.defaultTax) {
        return res.send({
            error: true,
            msg: "Please select a default tax in settings"
        })
    }

    await db.collection("recurringInvoices").add({
        created: Date.now(),
        invoiceID: null,
        ownerID: uid,
        period: invoice.period,
        startDate: invoice.startDate,
        nextCharge: invoice.startDate,
        title: invoice.title || "",
        reference: invoice.reference || "",
        rebill_title: invoice.rebill_title || "",
        rebill_description: invoice.rebill_description || "",
        customerID: invoice.customerID,
        customerDetails: {
            name: customerData.name_1,
            email: customerData.mail,
            pfp: customerData.profile_image
        },
        items: invoice.items,
        taxID: userData.settings.defaultTax,
        status: "active",
        history: [],
        mwst_type: invoice.taxStatus,
        updated: Date.now(),
        taxStatus: invoice.taxStatus,
        currency: userData.settings.defaultCurrency || 1
    } as RecurringInvoice)

    if (draftID) {
        // Check the draft belongs to the user and delete it
        const draft = await db.collection("drafs").doc(draftID).get().then((s) => s.data() as DBInvoiceDraft)
        if (draft.owner === uid) {
            db.collection("drafts").doc(draftID).delete()
        }
        // We won't do anything if this is invalid since the invoice has been created and this is only a secondary action
    }

    res.send({
        error: false,
        data: "Created successfully"
    })
}

export const updateInvoice = async (req: Request, res: Response) => {
    const { uid } = res.locals
    const { invoiceID, newInvoiceData } = req.body as { invoiceID: string, newInvoiceData: RecurringInvoice }
    const db = getFirestore()

    getAndValidateRecurringInvoice(invoiceID, uid).then(async (invoiceData) => {
        if (!newInvoiceData) {
            return res.send({
                error: true,
                msg: "Please provide a valid update object"
            })
        }

        await db.collection("recurringInvoices").doc(invoiceID).update({
            ...invoiceData,
            ...newInvoiceData,
            nextCharge: newInvoiceData.startDate !== invoiceData.startDate ? newInvoiceData.startDate : invoiceData.nextCharge,
            uid,
            error_retries: 0
        } as RecurringInvoice)
        res.send({
            error: false,
            data: "Recurring invoice updated. Changes will apply to your next invoice."
        })
    }).catch(err => res.send({ error: true, msg: err }))
}

export const pauseRecurringBilling = async (req: Request, res: Response) => {
    const { uid } = res.locals
    const { invoiceID } = req.body

    getAndValidateRecurringInvoice(invoiceID, uid).then(async () => {
        const db = getFirestore()
        await db.collection("recurringInvoices").doc(invoiceID).update({
            status: "in-active",
        } as Partial<RecurringInvoice>)
        res.send({
            error: false,
            data: "Billing paused"
        })
    }).catch(err => res.send({
        error: true,
        msg: err
    }))
}

export const restartRecurringBilling = (req: Request, res: Response) => {
    const { uid } = res.locals
    const { invoiceID } = req.body

    getAndValidateRecurringInvoice(invoiceID, uid).then(async () => {
        const db = getFirestore()
        await db.collection("recurringInvoices").doc(invoiceID).update({
            status: "active",
        } as Partial<RecurringInvoice>)
        res.send({
            error: false,
            data: "Billing enabled"
        })
    }).catch(err => res.send({
        error: true,
        msg: err
    }))
}

export const deleteRecurringBilling = async (req: Request, res: Response) => {
    const { uid } = res.locals
    const { invoiceID } = req.body

    getAndValidateRecurringInvoice(invoiceID, uid).then(async () => {
        const db = getFirestore()
        await db.collection("recurringInvoices").doc(invoiceID).update({
            status: "in-active",
            DELETED: Date.now()
        } as Partial<RecurringInvoice>)
        res.send({
            error: false,
            data: "Billing deleted"
        })
    }).catch(err => res.send({
        error: true,
        msg: err
    }))
}