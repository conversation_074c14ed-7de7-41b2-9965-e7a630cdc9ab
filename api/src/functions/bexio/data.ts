import axios from "axios";
import { Request, response, Response } from "express";
import { BexioContact, DraftCustomerDetails } from "../../types/global";
import bexio from "../../utils/bexio";

export const getBusinessData = async (_: Request, res: Response) => {
    const { access_token } = res.locals

    bexio.get({
        path: `/company_profile`,
        access_token
    }).then((response) => {
        console.log(response[0])
        res.send({
            error: false,
            // @ts-ignore
            data: response[0]
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch company profile. Make sure API Key has been entered correctly."
        })
    })
}

export const getInvoices = async (_: Request, res: Response) => {
    const { access_token } = res.locals

    bexio.get({
        path: `/kb_invoice`,
        access_token,
        body: {
            limit: 10_000
        }
    }).then((response) => {
        console.dir(response, { depth: null })
        res.send({
            error: false,
            data: response
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch invoices. Make sure API Key has been entered correctly."
        })
    })
}

export const getTaxes = async (_: Request, res: Response) => {
    const { access_token } = res.locals
    bexio.get({
        path: `/taxes?types=sales_tax&scope=active`,
        access_token, apiVersion: "3.0"
    }).then((response) => {
        res.send({
            error: false,
            data: response
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch taxes. Make sure API Key has been entered correctly."
        })
    })
}

export const getCurrencies = async (_: Request, res: Response) => {
    const { access_token } = res.locals
    bexio.get({
        path: `/currencies`,
        access_token,
        apiVersion: "3.0"
    }).then((response) => {
        res.send({
            error: false,
            data: response
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch currencies. Make sure API Key has been entered correctly."
        })
    })
}

export const getAccounts = async (_: Request, res: Response) => {
    const API_KEY = res.locals.bexioKey
    const { access_token } = res.locals
    bexio.get({
        path: `/accounts`,
        access_token
    }).then(response => {
        res.send({
            error: false,
            data: response
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch accounts. Make sure API Key has been entered correctly."
        })
    })
}

export const getContacts = async (_: Request, res: Response) => {
    const { access_token } = res.locals
    bexio.get({
        path: "/contact",
        access_token,
    }).then((response) => {
        res.send({
            error: false,
            data: response
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch contacts. Make sure API Key has been entered correctly."
        })
    })
}

export const getContact = async (req: Request, res: Response) => {
    const { id } = req?.body
    const { access_token } = res.locals
    bexio.get({
        path: `/contact/${id}`,
        access_token,
    }).then((response) => {
        res.send({
            error: false,
            data: response
        })
    }).catch(() => {
        res.send({
            error: true,
            msg: "Failed to fetch contact. Make sure API Key has been entered correctly."
        })
    })
}

export const getUnits = async (_: Request, res: Response) => {
    const { access_token } = res.locals
    bexio.get({
        path: `/unit`,
        access_token
    }).then(response => {
        res.send({
            error: false,
            data: response
        })
    }).catch((err) => {
        console.log(err)
        res.send({
            error: true,
            msg: "Failed to fetch units. Make sure API Key has been entered correctly."
        })
    })

}