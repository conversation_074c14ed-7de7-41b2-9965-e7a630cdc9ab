import express from 'express'
import { createCustomer, createInvoice, deleteRecurringBilling, pauseRecurringBilling, restartRecurringBilling, updateInvoice } from '../functions/bexio/actions'
import { getBusinessData, getContact, getContacts, getCurrencies, getInvoices, getTaxes, getUnits } from '../functions/bexio/data'
import { bexioAPIMiddleware } from '../utils/auth/bexioApiKeymiddleware'

const router = express.Router()

router.use(bexioAPIMiddleware)

router.post("/get-business", getBusinessData)
router.post("/get-contacts", getContacts)
// @ts-ignore
router.post("/get-contact", getContact)
router.post("/get-units", getUnits)
router.post("/get-invoices", getInvoices)
router.post("/get-taxes", getTaxes)
router.post("/get-currencies", getCurrencies)

router.post("/create-customer", createCustomer)
router.post("/create-invoice", createInvoice)
router.post("/update-invoice", updateInvoice)
router.post("/start-invoice", restartRecurringBilling)
router.post("/delete-invoice", deleteRecurringBilling)
router.post("/pause-invoice", pauseRecurringBilling)

export const BexioRouter = router