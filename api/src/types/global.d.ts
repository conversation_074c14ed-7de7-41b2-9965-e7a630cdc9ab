export type Language = "en" | "de"
export interface User {
    name: string,
    company?: {
        name: string,
        id: string,
        pfp?: string
    },
    email: string,
    created: number,
    bexioLinked: boolean,
    settings: {
        defaultTax?: number,
        defaultCurrency: number
        templates: {
            email: {
                subject: string,
                body: string,
                language: Language
            }
        }
    },
    update?: (newUser: Partial<User>) => void
}

export type BexioDataContextType = {
    taxes: Array<BexioTax>,
    units: Array<BexioUnit>,
    accounts: Array<BexioAccount | null>
    reload: () => void;
}

export interface APIResponse {
    data: any,
    msg: string,
    error: boolean
}

export type BexioCompanyProfile = {
    id: number;
    name: string;
    address: string;
    address_nr: string;
    postcode: string;
    city: string;
    country_id: number;
    legal_form: string;
    country_name: string;
    mail: string | null;
    phone_fixed: string | null;
    phone_mobile: string | null;
    fax: string | null;
    url: string | null;
    skype_name: string | null;
    facebook_name: string | null;
    twitter_name: string | null;
    description: string | null;
    ust_id_nr: string | null;
    mwst_nr: string;
    trade_register_nr: string | null;
    has_own_logo: boolean;
    is_public_profile: boolean;
    is_logo_public: boolean;
    is_address_public: boolean;
    is_phone_public: boolean;
    is_mobile_public: boolean;
    is_fax_public: boolean;
    is_mail_public: boolean;
    is_url_public: boolean;
    is_skype_public: boolean;
    logo_base64: string;
};

export type BexioAccount = {
    id: number;
    account_no: string;
    name: string;
    account_group_id: number;
    account_type: number;
    tax_id: number;
    is_active: boolean;
    is_locked: boolean;
};

export type BexioContact = {
    id: number;
    nr: string;
    contact_type_id: number;
    name_1: string;
    name_2: string | null;
    salutation_id: number;
    salutation_form: string | null;
    title_id: number | null;
    birthday: string | null;
    address: string;
    postcode: number;
    city: string;
    country_id: number;
    mail: string;
    mail_second: string;
    phone_fixed: string;
    phone_fixed_second: string;
    phone_mobile: string;
    fax: string;
    url: string;
    skype_name: string;
    remarks: string;
    language_id: number | null;
    is_lead: boolean;
    contact_group_ids: string;
    contact_branch_ids: string | null;
    user_id: number;
    owner_id: number;
    profile_image?: string;
    updated_at: string;
};

export type BexioInvoice = {
    id: number;
    document_nr: string;
    title: null | string;
    contact_id: number;
    contact_sub_id: null | number;
    user_id: number;
    project_id: null | number;
    logopaper_id: number;
    language_id: number;
    bank_account_id: number;
    currency_id: number;
    payment_type_id: number;
    header: string;
    footer: string;
    total_gross: string;
    total_net: string;
    total_taxes: string;
    total_received_payments: string;
    total_credit_vouchers: string;
    total_remaining_payments: string;
    total: string;
    total_rounding_difference: number;
    mwst_type: number;
    mwst_is_net: boolean;
    show_position_taxes: boolean;
    is_valid_from: string;
    is_valid_to: string;
    contact_address: string;
    kb_item_status_id: number;
    reference: null | string;
    api_reference: null | string;
    viewed_by_client_at: null | string;
    updated_at: string;
    esr_id: number;
    qr_invoice_id: number;
    template_slug: string;
    taxs: {
        percentage: string;
        value: string;
    }[];
    network_link: string;


    // For invoice history
    error?: {
        message: string,
        timestamp: number
    },
    rebill_timestamp: number
}

export type BexioInvoiceCreatePayload = {
    title: null | string;
    contact_id: number;
    contact_sub_id: null | number;
    user_id: number;
    pr_project_id: null | number;
    logopaper_id: number;
    language_id: number;
    bank_account_id: number;
    currency_id: number;
    payment_type_id: number;
    header: string;
    footer: string;
    mwst_type: number;
    mwst_is_net: boolean;
    show_position_taxes: boolean;
    is_valid_from: string;
    is_valid_to: string;
    reference: null | string;
    api_reference: null | string;
    template_slug: string;
    positions: {
        amount: string;
        unit_id: number;
        account_id: number;
        tax_id: number;
        text: string;
        unit_price: string;
        discount_in_percent: string;
        type: string;
        parent_id: null | number;
    }[];
}

export type BexioUnit = {
    name: string,
    id: number
}

export type BexioTax = {
    id: number;
    uuid: string;
    name: string;
    code: string;
    digit: string;
    type: string;
    account_id: number;
    tax_settlement_type: string;
    value: number;
    net_tax_value: null | number;
    start_year: number;
    end_year: number;
    is_active: boolean;
    display_name: string;
}

export type BexioCurrency = {
    id: number,
    name: string,
    round_factor: number,
    exchange_rate: number,
    exchange_rate_id: number,
    ratio: number,
    exchange_rate_to_ratio: number,
    source: string,
    source_reason: string,
    exchange_rate_date: string
}

export type BexioTaxStatus = 0 | 1 | 2

export type BillingPeriod = "monthly" | "yearly"

export type LiteCustomerDetails = {
    name: string,
    email: string,
    pfp: string
}

export type RecurringInvoice = {
    id?: string,
    ownerID: string,
    invoiceID: string | null,
    period: BillingPeriod,
    startDate: number,
    endDate?: number,
    created: number,
    nextCharge: number,
    title: string,
    reference: string,
    rebill_title: string,
    rebill_description: string,
    customerID: string,
    customerDetails: LiteCustomerDetails,
    // description: string,
    history: Array<BexioInvoice>,
    items: Array<DraftItem>,
    taxID: number,
    taxStatus: BexioTaxStatus,
    status: "active" | "in-active" | "deleted",
    updated: number,
    currency?: number
    error_retries?: number
    DELETED?: number
}

export type DraftInvoiceDetails = {
    title: string,
    reference: string,
    // description: string,
    rebill_title: string,
    rebill_description: string,
    customerID: string,
    customerDetails: LiteCustomerDetails | null,
    period: BillingPeriod,
    startDate: number | Date,
    items: Array<DraftItem>,
    taxStatus: BexioTaxStatus
}

export type DraftCustomerDetails = {
    name: string,
    type: 1 | 2,
    email: string,
    address: string,
    postcode: string,
    city: string
}

export type DraftItem = {
    name: string,
    unitQ: number,
    unitID: number,
    unitPrice: number,
    tax: number
}

export type DBInvoiceDraft = {
    draft: Partial<DraftInvoiceDetails>,
    owner: string,
    created: number
}

