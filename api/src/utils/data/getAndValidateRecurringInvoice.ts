import { getFirestore } from "firebase-admin/firestore"
import { RecurringInvoice } from "../../types/global"

export default (invoiceID: string, uid: string) => new Promise<RecurringInvoice>(async (res, rej) => {
    const db = getFirestore()
    const invoice = await (db.collection("recurringInvoices").doc(invoiceID).get().then(snap => snap.data())) as RecurringInvoice

    if (!invoice) {
        return rej("That invoice does not exist")
    }
    if (invoice.ownerID !== uid) {
        return rej("You do not have permission to update that invoice")
    }
    if (invoice.DELETED) {
        return rej(`You deleted this reucrring billing object on ${new Date(invoice.DELETED).toString()}`)
    }

    res(invoice)
})