import axios, { AxiosHeaders, AxiosRequestConfig, AxiosRequestHeaders, HeadersDefaults, Method } from "axios"

type RequestConfig = {
    path: string,
    body?: any,
    headers?: Partial<AxiosRequestHeaders>,
    access_token: string,
    apiVersion?: string
}

const request = ({ path, body, headers, access_token, apiVersion }: RequestConfig, method: "get" | "post") => new Promise<any>((res, rej) => {
    const extendedHeaders = {
        ...headers,
        Accept: "application/json",
        Authorization: `Bearer ${access_token}`
    } as Partial<AxiosRequestHeaders>
    axios[method](`https://api.bexio.com/${apiVersion || "2.0"}${path}`, method === "post" ? body : {
        headers: { ...extendedHeaders },
        params: { ...body }
    } as AxiosRequestConfig, method === "post" ? {
        headers: { ...extendedHeaders }
    } as AxiosRequestConfig : undefined).then((response) => {
        res(response.data)
    }).catch((err) => {
        console.log(err.response)
        rej("Something went wrong")
    })

})

export default {
    get: (config: RequestConfig) => request(config, "get"),
    post: (config: RequestConfig) => request(config, "post")
}