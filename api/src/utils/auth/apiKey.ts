import { Storage } from '@google-cloud/storage'
import { db } from '../firebase/firebase'
import { User } from '../../types/global'
import { storage } from '../gcloud/storage'

export const getToken = async (uid: string) => {
    const key = await storage.bucket("rebill-tokens").file(`${uid}/token.txt`).download()
    return key.toString()
}

export const setToken = async (uid: string, key: string) => {
    await storage.bucket("rebill-tokens").file(`${uid}/token.txt`).save(key)
    await db.collection("users").doc(uid).update({
        bexioLinked: true
    } as Partial<User>)
    return
}

export const removeToken = async (uid: string) => {
    await storage.bucket("rebill-tokens").file(`${uid}/token.txt`).delete()
    await db.collection("users").doc(uid).update({
        bexioLinked: false
    } as Partial<User>)
    return
}