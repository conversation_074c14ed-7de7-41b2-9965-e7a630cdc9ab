import { Request, Response } from 'express'
import { getAuth } from 'firebase-admin/auth'

export const authenticate = (req: Request, res: Response, next: () => void) => {
    if (req.headers["x-auth"]) {
        getAuth().verifyIdToken(req.headers["x-auth"] as string)
            .then((decoded) => {
                res.locals.uid = decoded.uid
                next()
            })
            .catch((err) => {
                console.log(err)
                res.sendStatus(401)
            })
    } else {
        res.send({
            error: true,
            msg: "You must provide a valid 'x-auth' header containing your authentication token"
        })
    }
}