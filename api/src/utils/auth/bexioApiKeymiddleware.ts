import { NextFunction, Request, Response } from "express";
import { getToken } from "./apiKey";
import axios from "axios";

export const generateAccessToken = async (uid: string): Promise<string> => {
    const refresh_token = await getToken(uid)

    return await axios.post(`https://auth.bexio.com/realms/bexio/protocol/openid-connect/token`, {
        refresh_token,
        grant_type: "refresh_token",
        client_id: "1f4ac48c-53c7-4bee-96ca-eac904753097",
        client_secret: "P4HKi6e96mKov9kcGbaNjT3gVfDgoV5dB7Jd_m_JWYmv7LHFJ2syA0DQVgMvlpQoh9T6AKwKjdMFSo3NEj-tOw"
    }, {
        headers: {
            "Content-Type": "application/x-www-form-urlencoded"
        }
    }).then((response) => {
        const { access_token } = response.data
        return access_token
    }).catch(err => {
        throw err
    })
}

export const bexioAPIMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    const { uid } = res.locals

    try {
        const access_token = await generateAccessToken(uid);

        res.locals.access_token = access_token
        next()

        // const refresh_token = await getToken(uid)

        // axios.post(`https://auth.bexio.com/realms/bexio/protocol/openid-connect/token`, {
        //     refresh_token,
        //     grant_type: "refresh_token",
        //     client_id: "1f4ac48c-53c7-4bee-96ca-eac904753097",
        //     client_secret: "P4HKi6e96mKov9kcGbaNjT3gVfDgoV5dB7Jd_m_JWYmv7LHFJ2syA0DQVgMvlpQoh9T6AKwKjdMFSo3NEj-tOw"
        // }, {
        //     headers: {
        //         "Content-Type": "application/x-www-form-urlencoded"
        //     }
        // }).then((response) => {
        //     const { access_token } = response.data
        //     res.locals.access_token = access_token
        //     next()
        // }).catch(err => {
        //     console.dir(err.response?.data, { depth: null })
        //     throw err
        // })
    } catch (err) {
        console.dir((err as any).response, { depth: null })
        res.send({
            error: false,
            redirect: "/setup"
        })
    }
}