import { RecurringInvoice } from "../../types/global";

export default (
    string: string,
    invoice: RecurringInvoice,
    currency: string
): string => {
    if (!string) return string;
    let newString = string
    newString = newString.replaceAll("[year]", new Date().getFullYear().toString())
    newString = newString.replaceAll("[month]", (new Date().getMonth() + 1).toString().padStart(2, "0"))
    newString = newString.replaceAll("[nextyear]", (new Date().getFullYear() + 1).toString())
    newString = newString.replaceAll("[nextmonth]", (new Date().getMonth() + 2).toString().padStart(2, "0"))

    newString = newString.replaceAll("[Date]", new Date().toLocaleDateString())
    newString = newString.replaceAll("[Total]", invoice.items.reduce((prev, current) => prev + (current.unitPrice * current.unitQ), 0).toString())
    newString = newString.replaceAll("[Payable By]", new Date(invoice.nextCharge).toLocaleDateString())
    newString = newString.replaceAll("[Currency]", currency || "CHF")

    return newString
}