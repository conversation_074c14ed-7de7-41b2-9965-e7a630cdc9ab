import { BexioInvoiceCreatePayload, DraftInvoiceDetails, User } from "../../types/global"
import bexio from "../bexio"

export const createBexioInvoice = async (access_token: string, invoice: DraftInvoiceDetails, tax: number, currency: number) => {
    return await bexio.post({
        path: `/kb_invoice`,
        access_token,
        body: {
            title: invoice.title,
            contact_id: parseInt(invoice.customerID),
            is_valid_from: new Date().toDateString(),
            reference: invoice.reference,
            positions: invoice.items.map(i => ({
                amount: i.unitQ.toString(),
                unit_id: i.unitID,
                text: i.name,
                unit_price: i.unitPrice.toString(),
                tax_id: tax,
                type: "KbPositionCustom"
            })),
            mwst_type: invoice.taxStatus,
            user_id: 1,
            currency_id: currency
        } as Partial<BexioInvoiceCreatePayload>
    })
}

export const sendInvoice = async (access_token: string, invoiceID: number, recipient_email: string, template: {
    body: string,
    subject: string
}) => {
    return await bexio.post({
        path: `/kb_invoice/${invoiceID}/send`,
        access_token,
        body: {
            recipient_email,
            subject: template?.subject,
            message: template?.body,
            mark_as_open: true,
            attach_pdf: true
        }
    })
}