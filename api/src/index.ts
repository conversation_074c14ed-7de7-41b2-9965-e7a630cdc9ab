import cors from 'cors'
import dotenv from 'dotenv'
dotenv.config()
const PORT = process.env.PORT || 8080

import express from 'express'
import { BexioRouter } from './routers/bexio.router'

import { authenticate } from './utils/auth/authenticate'
import { FulfillmentRouter } from './routers/fulfillment.router'
import { OauthRouter } from './routers/oauth.router'
import { fulfillInvoices } from './functions/fulfillment/invoices'

const app = express()

// @ts-ignore
// fulfillInvoices()
app.use(cors())
app.use(express.urlencoded({ extended: false }));
app.use(express.json());

app.use("/fulfillment", FulfillmentRouter)

app.use(authenticate)

app.use("/bexio", BexioRouter)
app.use("/oauth", OauthRouter)

app.listen(PORT, () => console.log(`[DEBUG] Live on port ${PORT}`))
